import 'package:flutter/material.dart';
import 'package:estimat_keymoments/l10n/app_localizations.dart';
import '../widgets/app_menu.dart';

class FullPresentationScreen extends StatefulWidget {
  const FullPresentationScreen({super.key});

  @override
  State<FullPresentationScreen> createState() => _FullPresentationScreenState();
}

class _FullPresentationScreenState extends State<FullPresentationScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  final int _totalPages = 8;

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _nextPage() {
    if (_currentPage < _totalPages - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.viewFullPresentationMenu),
        backgroundColor: const Color(0xFF2A6BAA),
        actions: const [
          AppMenu(),
        ],
      ),
      body: Column(
        children: [
          // Page indicator
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(_totalPages, (index) {
                return Container(
                  margin: const EdgeInsets.symmetric(horizontal: 4.0),
                  width: 8.0,
                  height: 8.0,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _currentPage == index ? Colors.blue : Colors.grey[300],
                  ),
                );
              }),
            ),
          ),
          Expanded(
            child: PageView(
              controller: _pageController,
              onPageChanged: (page) {
                setState(() {
                  _currentPage = page;
                });
              },
              children: [
                _buildIntroductionPage(context, localizations),
                _buildWhyMomentsPage(context, localizations),
                _buildMethodologyPage(context, localizations),
                _buildFourLevelsPage(context, localizations),
                _buildDirectionsPage(context, localizations),
                _buildLatentValuesPage(context, localizations),
                _buildApplicationPage(context, localizations),
                _buildConclusionPage(context, localizations),
              ],
            ),
          ),
          // Navigation buttons
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                ElevatedButton(
                  onPressed: _currentPage > 0 ? _previousPage : null,
                  child: Text(localizations.previousButton),
                ),
                Text(localizations.pageCounter(_currentPage + 1, _totalPages)),
                ElevatedButton(
                  onPressed: _currentPage < _totalPages - 1 ? _nextPage : null,
                  child: Text(localizations.nextButton),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIntroductionPage(BuildContext context, AppLocalizations localizations) {
    return _buildPage(
      context,
      '🎯',
      localizations.welcomeToEstimatKeyMoments,
      localizations.estimatKeyMomentsDescription,
      Colors.deepPurple.shade100,
    );
  }

  Widget _buildWhyMomentsPage(BuildContext context, AppLocalizations localizations) {
    return _buildPage(
      context,
      '❓',
      localizations.whyFocusOnMomentsTitle,
      localizations.whyFocusOnMomentsContent,
      Colors.blue.shade100,
    );
  }

  Widget _buildMethodologyPage(BuildContext context, AppLocalizations localizations) {
    return _buildPage(
      context,
      '🔬',
      'The Methodology',
      'Our approach combines psychological insights with practical analysis:\n\n'
      '1. **Moment Identification**: Recognize key moments in your life\n'
      '2. **Dimensional Analysis**: Categorize across four levels\n'
      '3. **Directional Focus**: Understand inward vs outward orientation\n'
      '4. **Impact Assessment**: Evaluate motivation and satisfaction effects\n'
      '5. **Pattern Recognition**: Discover your latent values',
      Colors.green.shade100,
    );
  }

  Widget _buildFourLevelsPage(BuildContext context, AppLocalizations localizations) {
    return _buildPage(
      context,
      '🏗️',
      'Four Levels of Human Experience',
      '**${localizations.elementalLabel}**: Physical and instinctual responses\n'
      '**${localizations.personalLabel}**: Individual experiences and emotions\n'
      '**${localizations.informationalLabel}**: Intellectual and analytical processes\n'
      '**${localizations.socialLabel}**: Interpersonal and cultural connections\n\n'
      'These levels work hierarchically, building upon each other to create your complete experience.',
      Colors.amber.shade100,
    );
  }

  Widget _buildDirectionsPage(BuildContext context, AppLocalizations localizations) {
    return _buildPage(
      context,
      '🧭',
      'Directional Focus',
      '**${localizations.inwardLabel}**: ${localizations.inwardExplanation}\n\n'
      '**${localizations.outwardLabel}**: ${localizations.outwardExplanation}\n\n'
      'Every moment has both inward and outward components, but one direction typically dominates. Understanding this helps reveal your natural tendencies and preferences.',
      Colors.purple.shade100,
    );
  }

  Widget _buildLatentValuesPage(BuildContext context, AppLocalizations localizations) {
    return _buildPage(
      context,
      '💎',
      localizations.discoveringLatentValuesTitle,
      localizations.discoveringLatentValuesContent(
        localizations.guardianLabel,
        localizations.warriorLabel,
        localizations.versatileLabel,
        localizations.funLabel,
      ),
      Colors.red.shade100,
    );
  }

  Widget _buildApplicationPage(BuildContext context, AppLocalizations localizations) {
    return _buildPage(
      context,
      '🎯',
      'Practical Application',
      'Use this methodology to:\n\n'
      '• **Track patterns** in your decision-making\n'
      '• **Identify triggers** for motivation and satisfaction\n'
      '• **Understand conflicts** between different aspects of yourself\n'
      '• **Make better choices** aligned with your values\n'
      '• **Develop strategies** for personal growth',
      Colors.teal.shade100,
    );
  }

  Widget _buildConclusionPage(BuildContext context, AppLocalizations localizations) {
    return _buildPage(
      context,
      '🌟',
      'Your Journey Begins',
      'Now that you understand the methodology, you can:\n\n'
      '• Start recording your key moments\n'
      '• Analyze your patterns over time\n'
      '• Discover your unique latent values\n'
      '• Use insights for personal development\n\n'
      'Remember: Self-awareness is the first step toward intentional living.',
      Colors.orange.shade100,
    );
  }

  Widget _buildPage(BuildContext context, String emoji, String title, String content, Color backgroundColor) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24.0),
      decoration: BoxDecoration(
        color: backgroundColor,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            emoji,
            style: const TextStyle(fontSize: 80),
          ),
          const SizedBox(height: 24),
          Text(
            title,
            style: const TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          Expanded(
            child: SingleChildScrollView(
              child: Text(
                content,
                style: const TextStyle(
                  fontSize: 18,
                  color: Colors.black87,
                  height: 1.6,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
