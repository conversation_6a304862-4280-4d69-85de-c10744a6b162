// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Estimat KeyMoments';

  @override
  String get selectLanguageTitle => 'Select Language';

  @override
  String get englishLanguage => 'English';

  @override
  String get portugueseLanguage => 'Portuguese';

  @override
  String get spanishLanguage => 'Spanish';

  @override
  String get onboardingScreenTitle => 'Welcome to Estimat KeyMoments';

  @override
  String get onboardingScene1Message =>
      'Noise can be simplified into notes and turned into music';

  @override
  String get onboardingScene1MessagePart2 => 'With a bit of proportion...';

  @override
  String get onboardingScene2Message =>
      'Any image can be replicated through light, shadow, and color, and transformed into art';

  @override
  String get onboardingScene2MessagePart2 => 'With a bit of proportion...';

  @override
  String get onboardingScene3Message =>
      'Your cognitive overload—could you refine what you value most in yourself?';

  @override
  String get onboardingScene3MessagePart2 => 'With a bit of ...';

  @override
  String get onboardingScene4Message =>
      'With information theory, an AI can reduce complex data to 5% and reconstruct it with 95% functional fidelity; these are latent vectors';

  @override
  String get onboardingScene4MessagePart2 =>
      'Maybe you could apply a bit of that to your latent values...';

  @override
  String get onboardingFlowchartMessage =>
      'Let\'s identify our reference moments. Track the flows associated with those moments. Evaluate according to their function. Discover the latent values that drive us:';

  @override
  String get onboardingFlowchartTitle => 'Process Flow';

  @override
  String get nextButton => 'Next';

  @override
  String get previousButton => 'Previous';

  @override
  String get getStartedButton => 'Get Started';

  @override
  String get exitToAppButton => 'Exit to App';

  @override
  String get motivationAppBarTitle => 'Motivation Moments';

  @override
  String get satisfactionAppBarTitle => 'Satisfaction Moments';

  @override
  String get selectMomentTypeTitle => 'Select Moment Type';

  @override
  String get selectImprovementWorsening =>
      'Choose between Improvement or Worsening:';

  @override
  String get selectMotivationSatisfaction =>
      'Choose between Motivation or Satisfaction:';

  @override
  String get improvesLabel => 'Improves';

  @override
  String get worsensLabel => 'Worsens';

  @override
  String get motivationLabel => 'Motivation';

  @override
  String get satisfactionLabel => 'Satisfaction';

  @override
  String get elementalLabel => 'Elemental';

  @override
  String get personalLabel => 'Personal';

  @override
  String get informationalLabel => 'Informational';

  @override
  String get socialLabel => 'Social';

  @override
  String get elementalExplanation =>
      'Based on environmental stimuli, we give a response that was modulated by evolution. More correlated with physical activities: Recognition, consumption, rest, storage, execute, fight, attack, and struggle.';

  @override
  String get personalExplanation =>
      'Adding to the memory that comes from our ancestors, we have a memory that is created from the unique interaction between an individual and their environment. ainful and pleasant. More correlated with: Error observation, adaptation, pain awareness, positive focus, pleasure, success enhancement';

  @override
  String get informationalExplanation =>
      'Upon reaching the intellectual level, in addition to integrating memories and experiences with the environment, we achieve a unique ability: to relate memories or an individual\'s own memories to each other and encode abstract information (example: Trend analysis, hypothesis creation, prediction, Generalization, efficiency, tracking).';

  @override
  String get socialExplanation =>
      'Social relationships involve the connection of information between individuals, the exchange of culture and communication. We can define a boundary between intellectual level and social level when we use our intellectual processes to interact with other beings capable of combining their own memories. To Empathy, consideration of others\' needs, Communication, cooperation, shared goals';

  @override
  String get inwardLabel => 'Inward';

  @override
  String get outwardLabel => 'Outward';

  @override
  String get inwardExplanation =>
      'Reflexive: To store, self-transform, predict, empathize.';

  @override
  String get outwardExplanation =>
      'Intuitive: To execute, enjoy, track, collaborate.';

  @override
  String get titleInputHint => 'Enter a two-word title';

  @override
  String get descriptionInputHint => 'Describe this moment';

  @override
  String get evidenceInputHint => 'Provide evidence for this distribution';

  @override
  String get describeDistributionHint =>
      'Describe why you chose this distribution...';

  @override
  String get firstMomentEvidenceHint =>
      'Explain why this moment creates more possibilities than just being alive. What new opportunities, paths, or potential does it open up?';

  @override
  String get comparisonEvidenceHint =>
      'Explain how many possibilities this moment creates compared to your life baseline...';

  @override
  String get lifePossibilitiesFactorTitle => 'Life Possibilities Factor';

  @override
  String get howManyPossibilitiesTitle =>
      'How Many Possibilities Does This Moment Create';

  @override
  String get comparedToLifeBaseline =>
      'Compared to your life baseline, this moment creates:';

  @override
  String get morePossibilitiesButton => 'More Possibilities';

  @override
  String get fewerPossibilitiesButton => 'Fewer Possibilities';

  @override
  String get vsLifeBaseline => 'vs Life Baseline';

  @override
  String get explainWhyFirstMomentTitle =>
      'Explain Why This Moment Creates More Possibilities Than Life';

  @override
  String get provideEvidenceTitle =>
      'Provide Evidence for this Possibilities Assessment';

  @override
  String get continueButton => 'Continue';

  @override
  String get lifePossibilitiesChart => 'Life Possibilities Chart';

  @override
  String get allTimeFilter => 'All Time';

  @override
  String get lastWeekFilter => 'Last Week';

  @override
  String get lastMonthFilter => 'Last Month';

  @override
  String get last3MonthsFilter => 'Last 3 Months';

  @override
  String get currentPreviewLabel => 'Current Preview:';

  @override
  String get lifeLabel => 'Life';

  @override
  String get previewLabel => 'Preview';

  @override
  String get lifeBaselineLabel => 'Life Baseline (1x)';

  @override
  String get morePossibilitiesLabel => 'More Possibilities';

  @override
  String get fewerPossibilitiesLabel => 'Fewer Possibilities';

  @override
  String get currentPreviewLegend => 'Current Preview';

  @override
  String get lifePossibilitiesExplanation =>
      'Compare this moment to your life baseline (1x). How many times more possibilities does this moment create for you? You can go above or below previous moments, but never below the life baseline.\\n\\nThe factorial calculation represents the exponential growth of possibilities that meaningful moments can create in your life.';

  @override
  String get minimumLifeBaselineNote => 'Minimum: 1.0x (life baseline)';

  @override
  String get guardianLabel => 'Guardian';

  @override
  String get warriorLabel => 'Warrior';

  @override
  String get versatileLabel => 'Versatile';

  @override
  String get funLabel => 'Fun';

  @override
  String get strategistLabel => 'Strategist';

  @override
  String get tacticalLabel => 'Tactical';

  @override
  String get altruistLabel => 'Altruist';

  @override
  String get collaboratorLabel => 'Collaborator';

  @override
  String get summaryTitle => 'Summary';

  @override
  String get motivationSectionTitle => 'Motivation Analysis';

  @override
  String get satisfactionSectionTitle => 'Satisfaction Analysis';

  @override
  String get latentValuesSectionTitle => 'Latent Values';

  @override
  String get improvesMotivationLabel => 'Improves Motivation';

  @override
  String get worsensMotivationLabel => 'Worsens Motivation';

  @override
  String get improvesSatisfactionLabel => 'Improves Satisfaction';

  @override
  String get worsensSatisfactionLabel => 'Worsens Satisfaction';

  @override
  String get proportionLabel => 'Proportion';

  @override
  String get exportDataButton => 'Export Data';

  @override
  String get viewOnboardingMenu => 'Onboarding';

  @override
  String get viewFullPresentationMenu => 'Full Presentation';

  @override
  String get viewLevelProcessFocusMenu => 'Levels and Directions';

  @override
  String get viewLatentValuesMenu => 'Evolutive Functions and Values';

  @override
  String get fromIACodeToHumanValuesMenu => 'From IA code to human values';

  @override
  String get jacoMinestSupportMenu => 'Jacominest Support';

  @override
  String get changeLanguageMenu => 'Change Language';

  @override
  String get saveButton => 'Save';

  @override
  String get cancelButton => 'Cancel';

  @override
  String get backButton => 'Back';

  @override
  String get whyMomentsScreenTitle => 'Why Moments?';

  @override
  String get whyMomentsMenuTitle => 'Why Moments?';

  @override
  String get whyHierarchicalMenuTitle => 'Why Hierarchical Organization?';

  @override
  String get whyHierarchicalScreenTitle => 'Why Hierarchical Organization';

  @override
  String get levelsAndDirectionsScreenTitle => 'Levels and Directions';

  @override
  String get skipForNowButton => 'Skip for now';

  @override
  String get noMomentsRecordedYet => 'No moments recorded yet.';

  @override
  String pageCounter(int current, int total) {
    return '$current / $total';
  }

  @override
  String get welcomeToEstimatKeyMoments => 'Welcome to Estimat KeyMoments';

  @override
  String get estimatKeyMomentsDescription =>
      'A comprehensive methodology for understanding and analyzing the key moments that shape your life decisions and personal growth.';

  @override
  String get editThisMomentButton => 'Edit this moment';

  @override
  String get closeButton => 'Close';

  @override
  String get insertNewMomentButton => 'Insert a new moment';

  @override
  String get viewAllMomentsButton => 'View All Moments';

  @override
  String get momentsHistoryTitle => 'Moments History';

  @override
  String momentSavedTitle(String momentType) {
    return '$momentType Saved';
  }

  @override
  String get momentSavedMessage => 'Your moment has been saved successfully.';

  @override
  String factorialComparisonLabel(String comparisonType) {
    return 'Factorial Comparison: $comparisonType';
  }

  @override
  String get improvementLabel => 'Improvement';

  @override
  String get worseningLabel => 'Worsening';

  @override
  String factorialSliderValueLabel(String value) {
    return 'Factorial Slider Value: $value';
  }

  @override
  String factorialMultiplierLabel(String value) {
    return 'Factorial Multiplier: $value';
  }

  @override
  String get whyRegisterMomentsTitle =>
      'Why does it matter to record moments more objectively?';

  @override
  String get whyRegisterMomentsContent =>
      'Your emotional peaks and valleys aren\'t just scattered data: they\'re like the coordinates of your internal compass. Recording your motivation (how driven you feel) and your satisfaction (how fulfilled you feel) gives you a clear map to plan goals, routines or tasks that really add to your life. This becomes key when you discover where these two things misalign, because there you can adjust and design activities that work better for you.';

  @override
  String get whenNoticeMoreTitle => 'When is this most noticeable?';

  @override
  String get whenNoticeMoreContent =>
      'When you\'re in an emotional low. In those moments, your brain tricks you with thoughts like: \"I never get anything right.\" \"There\'s nothing that motivates me.\" Although this isn\'t true, memory bias—that tendency to remember the negative or most recent—makes you feel that way. 📈 Keeping an objective record of your high moments gives you real proof of who you are when you\'re well, so you don\'t get lost in the fog when you\'re down.';

  @override
  String get highMotivationSatisfactionTitle =>
      'High motivation and high satisfaction';

  @override
  String get highMotivationSatisfactionContent =>
      'Quick question: What was the most significant thing that happened to you this month? If you had a daily record of your emotions, would you answer the same? Probably not. And there\'s a reason: Memory bias: According to Kahneman and Tversky (1979), our brain tends to give more weight to what\'s recent or intense (this is called the peak-end rule). That\'s why we sometimes forget valuable moments that weren\'t so \"noisy\". Solution: Writing down your peaks compensates for this mental trap and shows you the complete picture.';

  @override
  String get evolutionaryViewTitle => 'Evolutionary perspective';

  @override
  String get evolutionaryViewContent =>
      'Strong positive emotions—like pride or fulfillment—are signals of opportunities: a relationship that works, a personal achievement or a decision that aligns you with what you want. Our ancestors survived better if they remembered these moments to repeat them. Your brain isn\'t designed just to make you happy, but to help you thrive. If you know what motivates you, you can seek it more often.';

  @override
  String get practiceEstimatTitle => 'Practice with ESTIMAT:';

  @override
  String get practiceEstimatContent =>
      'Imagine you write down: ✍️ \"I felt powerful explaining my emotions in a workshop without being judged.\" 🔁 When reviewing several similar entries, ESTIMAT shows you: High motivation when you express what you feel. High satisfaction with active listening and validation. 🧭 What do you do with this? Look for or create more situations like this (workshops, talks with understanding friends). Use it as an anchor when you feel lost.';

  @override
  String get highMotivationLowSatisfactionTitle =>
      'High Motivation + Low Satisfaction';

  @override
  String get highMotivationLowSatisfactionContent =>
      'Have you ever been really excited about something and then felt \"meh\"? Example: You dream about the new iPhone 📱, rush to buy it and the next day think: \"Was this it?\" Impact bias: According to Gilbert and Wilson, we tend to overestimate future happiness by 40-60%. It\'s as if your brain puts an Instagram filter on what you expect, so you move towards it. Reality: When the moment arrives, it\'s not always up to the hype.';

  @override
  String get lowMotivationHighSatisfactionTitle =>
      'Low Motivation + High Satisfaction';

  @override
  String get lowMotivationHighSatisfactionContent =>
      'Have you ever dragged yourself to do something and ended up surprised by how good you felt? ⛈️😊 Example: You don\'t want to go to the gym, but you leave with a smile. Underestimation: Studies from the University of British Columbia show that we underestimate the pleasure of exercise by 20-30%. You think it will be +1, but it ends up being +3. Effort paradox: The effort itself gives you a satisfaction bonus—every step counts.';

  @override
  String get lowMotivationLowSatisfactionTitle =>
      'Low Motivation + Low Satisfaction';

  @override
  String get lowMotivationLowSatisfactionContent =>
      'What about those days when there\'s no desire or pleasure? 😔 You could ignore them or force yourself to continue, but what if those lows were like seeds for something bigger? Reflecting on those difficult moments not only helps you get out of the pit, but can teach you how to avoid falling so deep next time and even ignite new ideas. A 2020 study in Frontiers in Psychology explored how thinking deeply about problems can help when you\'re down. They found that people who reflected on their problems, seeking solutions, felt less depressed after a few weeks.';

  @override
  String get evolutiveFunctionsHeader => 'Evolutive Functions';

  @override
  String get valueDescriptionGuardian =>
      'Recognize: You recognize, consume, ingest.\nStore: You rest, store, and metabolize.';

  @override
  String get valueDescriptionWarrior =>
      'Discard: You discard, flee, inhibit.\nExecute: Fight, attack, and struggle.';

  @override
  String get valueDescriptionVersatile =>
      'Self-Observe: You highlight negative information, feel pain, observe errors.\nSelf-Transform: You reduce your errors and adapt stimuli.';

  @override
  String get valueDescriptionFunny =>
      'Self-Motivate: You highlight positive information, feel pleasure.\nSelf-Enjoy: I enhance successes and contrast attitudes and ideas.';

  @override
  String get valueDescriptionStrategist =>
      'Analyze: You review trends, ask if something could be false, analyze.\nPredict: You predict what is most probable and create hypotheses.';

  @override
  String get valueDescriptionTactician =>
      'Simplify: You generalize, compare the easiest, fastest way.\nTrack: You search, hunt, and trace.';

  @override
  String get valueDescriptionAltruistic =>
      'Empathize: You empathize with what is important for others.\nDeliberate: You consider myself and the needs of as many people as possible, practicing efficiency altruism.';

  @override
  String get valueDescriptionCollaborator =>
      'Negotiate: You help to understand, communicate.\nCollaborate: You cooperate toward shared goals.';

  @override
  String get latentValuesTitle => 'Latent Values';

  @override
  String get lifePossibilitiesChartTitle => 'Life Possibilities Chart';

  @override
  String get periodLabel => 'Period:';

  @override
  String get last7DaysFilter => 'Last 7 Days';

  @override
  String get customRangeFilter => 'Custom Range';

  @override
  String get customDateRangeFilterTitle => 'Custom Date Range Filter';

  @override
  String get startDateLabel => 'Start Date:';

  @override
  String get endDateLabel => 'End Date:';

  @override
  String get selectStartDateHint => 'Select start date';

  @override
  String get selectEndDateHint => 'Select end date';

  @override
  String get resetButton => 'Reset';

  @override
  String get customRangeActiveLabel => 'Custom range active';

  @override
  String showingMomentsLabel(int count, String plural) {
    return 'Showing $count moment$plural';
  }

  @override
  String get whyFocusOnMomentsTitle => 'Why Focus on Moments?';

  @override
  String get whyFocusOnMomentsContent =>
      'Key moments are the building blocks of our decision-making process. By understanding these moments, we can:\n\n• Identify patterns in our behavior\n• Understand what truly motivates us\n• Recognize what brings us satisfaction\n• Make more conscious choices\n• Develop better self-awareness';

  @override
  String get discoveringLatentValuesTitle => 'Discovering Latent Values';

  @override
  String discoveringLatentValuesContent(
    String guardianLabel,
    String warriorLabel,
    String versatileLabel,
    String funLabel,
  ) {
    return 'Your latent values emerge from the combination of:\n\n• **Level percentages** (how you distribute across the four levels)\n• **Directional focus** (inward vs outward orientation)\n• **Impact type** (improves vs worsens)\n\nThese values reveal your core strengths: $guardianLabel, $warriorLabel, $versatileLabel, $funLabel, and others.';
  }

  @override
  String get understandingLatentValuesTitle => 'Understanding Latent Values';

  @override
  String get understandingLatentValuesContent =>
      'Your latent values emerge from how you distribute your focus across the four hierarchical levels and whether you tend toward inward or outward orientation. Each combination reveals core strengths and natural tendencies that guide your decision-making and life satisfaction.';

  @override
  String get directionsFocusTitle => 'Directions Focus';

  @override
  String get whyHierarchicalOrganizationTitle =>
      'Why Hierarchical Organization of Life Moments Might Matter';

  @override
  String get dataVisualizationBiasReductionTitle =>
      'Data Visualization & Bias Reduction';

  @override
  String get experimentalEvidenceTitle => 'Experimental Evidence';

  @override
  String get evolutionaryPsychologyPerspectiveTitle =>
      'Evolutionary Psychology Perspective';

  @override
  String get recognizeYourMomentOrientationLevel =>
      'Recognize your moment, your orientation and level';

  @override
  String get recognizeYourMoment => 'Recognize your moment';

  @override
  String get organizeTheMomentAndLookForEvidence =>
      'Now let\'s look at the distribution of information at the moment and look for evidence.';

  @override
  String get orientationTitle => 'Orientation';

  @override
  String get orientationQuestion =>
      'Was your moment more focused on changing internally or externally?';

  @override
  String get threeLevelsTitle => 'Levels';

  @override
  String get threeLevelsDescription =>
      'Hierarchical analysis of your moment across the four levels of human experience';

  @override
  String get pieStepElementalPersonalTitle => '3 Levels: Elemental vs Personal';

  @override
  String get pieStepElementalPersonalDescription =>
      'For your moment you focused more on your body state or on your personal interests/emotions?';

  @override
  String get pieStepPersonalInformationalTitle =>
      '3 Levels: Personal vs Informational';

  @override
  String get pieStepPersonalInformationalDescription =>
      'For your moment you focused more on your personal interests/emotions or on your gathering and processing information?';

  @override
  String get pieStepInformationalSocialTitle =>
      '3 Levels: Informational vs Social';

  @override
  String get pieStepInformationalSocialDescription =>
      'For your moment you focused more on analyzing information or on connecting with others?';

  @override
  String get pieStepEvidenceQuestion =>
      'What evidence or variables influenced your choice?';

  @override
  String get whyMomentsHeaderSubtitle =>
      'Analyze your moments, know your patterns';

  @override
  String get whyRegisterMomentsObjectivelyTitle =>
      'Why register moments objectively?';

  @override
  String get whyRegisterMomentsObjectivelyContent =>
      'Your emotional peaks and valleys are not just loose data: they are like the coordinates of your internal compass. Recording your motivation (how driven you feel) and your satisfaction (how fulfilled you feel) gives you a clear map to plan goals, routines or tasks that really add to your life.';

  @override
  String get whyRegisterMomentsObjectivelyHighlight =>
      '📈 Keeping a record of your motivation and satisfaction peaks serves to compensate for emotional distortion. It allows you to have evidence of who you are when you\'re well, so you don\'t lose sight of yourself when you\'re not.';

  @override
  String get highMotivationHighSatisfactionTitle =>
      'High Motivation + High Satisfaction';

  @override
  String get researchInfoBoxTitle => '🧠 Research';

  @override
  String get researchInfoBoxContent =>
      'According to Kahneman and Tversky (1979), our brain tends to give more weight to what is recent or intense (peak-end rule). That\'s why we sometimes forget valuable moments that weren\'t so \"noisy\".';

  @override
  String get evolutionaryViewInfoBoxTitle => '🔬 Evolutionary View';

  @override
  String get evolutionaryViewInfoBoxContent =>
      'Strong positive emotions signal adaptive opportunities: successful relationships, purpose-aligned decisions, social or personal achievements.';

  @override
  String get practiceEstimatInfoBoxTitle => '✍️ Practice with ESTIMAT';

  @override
  String get practiceEstimatInfoBoxContent =>
      'By reviewing multiple entries, ESTIMAT shows you patterns and helps you replicate those moments of high motivation and satisfaction.';

  @override
  String get highMotivationLowSatisfactionIntro =>
      'Have you ever been really excited about something and then felt a \"meh\"? 📱';

  @override
  String get impactBiasInfoBoxTitle => '📊 Impact Bias';

  @override
  String get impactBiasInfoBoxContent =>
      'According to Gilbert and Wilson, we tend to overestimate future happiness by';

  @override
  String get practiceInfoBoxTitle => '🎯 Practice';

  @override
  String get practiceInfoBoxContent =>
      '• Simulate: Before diving in, imagine colors, smells, sounds\n• Prediction vs. reality: Write down how much you think you\'ll enjoy it\n• Adjust: If you expected +3 and it was +1, rethink if it\'s worth repeating';

  @override
  String get lowMotivationHighSatisfactionIntro =>
      'Have you ever dragged yourself to do something and ended up surprised by how good you felt? ⛈️😊';

  @override
  String get pleasureUnderestimationInfoBoxTitle =>
      '🏃‍♂️ Pleasure Underestimation';

  @override
  String get pleasureUnderestimationInfoBoxContent =>
      'The University of British Columbia showed that people underestimate their enjoyment of exercise by';

  @override
  String get effortParadoxInfoBoxTitle => '🧬 Effort Paradox';

  @override
  String get effortParadoxInfoBoxContent =>
      'Every drop of effort is exchanged for an extra plus of satisfaction. That \"burst of joy\" is your brain saying \"Well done!\"';

  @override
  String get lowMotivationLowSatisfactionIntro =>
      'And those days when there\'s no desire or pleasure? 😔 Those lows might be seedbeds for your best ideas.';

  @override
  String get reflectionPowerInfoBoxTitle => '📈 Power of Reflection';

  @override
  String get reflectionPowerInfoBoxContent =>
      'Students who practiced reflective self-evaluation showed an increase of';

  @override
  String get practiceEstimatLowInfoBoxTitle => '🎯 Practice with ESTIMAT';

  @override
  String get practiceEstimatLowInfoBoxContent =>
      '• Record your low: Write without filters how you feel\n• Review your log: ESTIMAT will show you patterns\n• Do something small: A walk, a song, three gratitudes';

  @override
  String get generalOverviewTitle => 'The General Overview';

  @override
  String get generalOverviewIntro =>
      'Recording your emotions is not just a hobby—it\'s a tool to know yourself and get the most out of your brain.';

  @override
  String get memoryBiasStatTitle => '🧠 Memory bias';

  @override
  String get memoryBiasStatContent => 'We overvalue what\'s recent or intense';

  @override
  String get impactBiasStatTitle => '🎯 Impact bias';

  @override
  String get impactBiasStatContent => 'We overestimate future happiness';

  @override
  String get underestimationStatTitle => '💪 Underestimation';

  @override
  String get underestimationStatContent =>
      'We enjoy exercise more than we think';

  @override
  String get recoveryStatTitle => '🔄 Recovery';

  @override
  String get recoveryStatContent => 'Reflecting gives you more motivated days';

  @override
  String get generalOverviewConclusion =>
      'What small step can you take today to better understand your emotions? Try writing down a peak and a valley—the results might surprise you. ✨';

  @override
  String get whyHierarchicalHeaderSubtitle =>
      'Why Hierarchical Organization of Life Moments Might Matter';

  @override
  String get whyHierarchicalImportantNote =>
      'Important: If you\'re already experiencing consistent well-being and deep satisfaction, additional organization is likely unnecessary. This approach seems most relevant during transitions, complex decisions, or persistent dissatisfaction.';

  @override
  String get informationTheoryPerspectiveTitle =>
      'Information Theory Perspective';

  @override
  String get debunkingCommonMythsTitle => 'Debunking Common Myths';

  @override
  String get selfPerceptionBiasesTitle =>
      'The Problem of Self-Perception Biases';

  @override
  String get visualProportionsTitle => 'Advantages of Visual Proportions';

  @override
  String get statsVsIntuitionTitle => 'Personal Statistics vs. Intuition';

  @override
  String get memoryHierarchyTitle => 'Memory Hierarchy Experiments';

  @override
  String get decisionFatigueTitle => 'Decision Fatigue Studies';

  @override
  String get millerNumberTitle => 'Miller\'s Magical Number';

  @override
  String get availabilityBiasContent =>
      'Availability Bias (Tversky & Kahneman, 1973): We\'re likely to disproportionately remember recent or emotional events, which could distort our perception of life patterns.';

  @override
  String get overestimationLabel => 'Overestimation';

  @override
  String get overestimationSublabel => 'of recent vs. actual patterns';

  @override
  String get decisionDistortionLabel => 'Decision Distortion';

  @override
  String get decisionDistortionSublabel => 'from memory-based choices';

  @override
  String get hierarchicalVisualizationNote =>
      'Hierarchical moment visualization might counteract this bias by providing a more objective representation of temporal patterns.';

  @override
  String get clevelandMcGillContent =>
      'Cleveland & McGill (1984): Visual perception of proportions appears to be significantly more accurate than narrative memories for evaluating temporal distributions.';

  @override
  String get potentialPersonalApplicationsTitle =>
      'Potential Personal Applications:';

  @override
  String get personalApplicationsList =>
      '• Actual time distribution vs. perception\n• Energy patterns and emotional states\n• Frequency of different experience types\n• Progress toward long-term objectives';

  @override
  String get visualizationDiscrepanciesNote =>
      'These visualizations could reveal discrepancies between subjective perception and objective reality, facilitating more informed decisions.';

  @override
  String get personalIntuitionParadoxContent =>
      'Personal Intuition Paradox: While we trust our intuition for personal decisions, we\'re likely to apply rigorous statistical analysis for professional or financial decisions.';

  @override
  String get financialDecisionsLabel => 'Financial Decisions';

  @override
  String get financialDecisionsSublabel => 'use objective data';

  @override
  String get personalDecisionsLabel => 'Personal Decisions';

  @override
  String get personalDecisionsSublabel => 'use objective data';

  @override
  String get potentialImprovementLabel => 'Potential Improvement';

  @override
  String get potentialImprovementSublabel => 'with systematic organization';

  @override
  String get hierarchicalAnalyticalNote =>
      'Hierarchical organization might allow applying analytical rigor to life decisions while maintaining emotional and intuitive flexibility.';

  @override
  String get socialLevelTitle => '4. Social';

  @override
  String get informationalLevelTitle => '3. Informational';

  @override
  String get personalLevelTitle => '2. Personal';

  @override
  String get elementalLevelTitle => '1. Elemental';

  @override
  String get collaborateFunction => 'Collaborate';

  @override
  String get negotiateFunction => 'Negotiate';

  @override
  String get collaboratorValue => 'COLLABORATOR';

  @override
  String get diplomatSubtitle => 'Diplomat';

  @override
  String get deliberateFunction => 'Deliberate';

  @override
  String get empathizeFunction => 'Empathize';

  @override
  String get altruisticValue => 'ALTRUISTIC';

  @override
  String get empatheticSubtitle => 'Empathetic';

  @override
  String get predictFunction => 'Predict';

  @override
  String get analyzeFunction => 'Analyze';

  @override
  String get strategistValue => 'STRATEGIST';

  @override
  String get analystSubtitle => 'Analyst';

  @override
  String get trackFunction => 'Track';

  @override
  String get simplifyFunction => 'Simplify';

  @override
  String get tacticianValue => 'TACTICIAN';

  @override
  String get synthesizerSubtitle => 'Synthesizer';

  @override
  String get selfEnjoyFunction => 'Self-Enjoy';

  @override
  String get selfMotivateFunction => 'Self-Motivate';

  @override
  String get funnyValue => 'FUNNY';

  @override
  String get enthusiasticSubtitle => 'Enthusiastic';

  @override
  String get selfTransformFunction => 'Self-Transform';

  @override
  String get selfObserveFunction => 'Self-Observe';

  @override
  String get versatileValue => 'VERSATILE';

  @override
  String get selfSeerSubtitle => 'SelfSeer';

  @override
  String get executeFunction => 'Execute';

  @override
  String get discardFunction => 'Discard';

  @override
  String get warriorValue => 'WARRIOR';

  @override
  String get releaserSubtitle => 'Releaser';

  @override
  String get storeFunction => 'Store';

  @override
  String get recognizeFunction => 'Recognize';

  @override
  String get guardianValue => 'GUARDIAN';

  @override
  String get nurturerSubtitle => 'Nurturer';

  @override
  String get memoryHierarchyContent =>
      'Bower et al. (1969): Hierarchical organization can improve recall by approximately 200% compared to random presentation.';

  @override
  String get baselineLabel => 'Baseline';

  @override
  String get randomPresentationSublabel => 'recall capacity';

  @override
  String get hierarchicalOrganizationLabel => 'Hierarchical Organization';

  @override
  String get hierarchicalImprovementSublabel => 'improvement in recall';

  @override
  String get brainProcessesHierarchically =>
      'Likely implication: Your brain probably processes information hierarchically by nature. Fighting against this structure possibly wastes cognitive resources.';

  @override
  String get decisionFatigueContent =>
      'Baumeister et al. (1998): After repeated unstructured decisions, decision quality probably declines significantly.';

  @override
  String get evolutionaryPerspectiveTitle => 'Evolutionary Perspective:';

  @override
  String get ancestorsDecisionsContent =>
      'Our ancestors probably faced limited daily decisions in structured social hierarchies. Modern chaos of disorganized choices may exceed our cognitive capacity.';

  @override
  String get preOrganizedStructures =>
      'Pre-organized hierarchical structures could maintain decision quality even under cognitive load.';

  @override
  String get millerNumberContent =>
      'Miller (1956): Humans can possibly maintain 7±2 unrelated items in working memory, but probably can process 7±2 categories, each containing 7±2 subcategories.';

  @override
  String get individualItemsLabel => 'Individual Items';

  @override
  String get workingMemoryLimitSublabel => 'working memory limit';

  @override
  String get hierarchicalCapacityLabel => 'Hierarchical Capacity';

  @override
  String get organizedElementsSublabel => 'organized elements';

  @override
  String get exponentialProcessingCapacity =>
      'This could create exponential processing capacity through hierarchy, freeing mental resources for pattern recognition and future planning.';

  @override
  String get ancestralMismatchContent =>
      'Modern humans possibly face approximately 35,000 daily decisions, while our ancestors probably encountered 70-100 structured decisions in predictable social hierarchies.';

  @override
  String get ancestralDecisionsLabel => 'Ancestral Decisions';

  @override
  String get structuredPerDaySublabel => 'structured/day';

  @override
  String get modernDecisionsLabel => 'Modern Decisions';

  @override
  String get unstructuredPerDaySublabel => 'unstructured/day';

  @override
  String get schwartzOptionsContent =>
      'Schwartz (2004): More than 8-10 unstructured options can decrease satisfaction by 25% and decision quality by 15%.';

  @override
  String get foragingEfficiencyContent =>
      'Stephens & Krebs (1986): Animals that organized foraging behavior hierarchically (territory → patches → specific resources) likely showed 40-60% better energy efficiency.';

  @override
  String get energyEfficiencyLabel => 'Energy Efficiency';

  @override
  String get hierarchicalOrganizationSublabel => 'hierarchical organization';

  @override
  String get goalAchievementLabel => 'Goal Achievement';

  @override
  String get structuredFrameworksSublabel => 'structured frameworks';

  @override
  String get gigerenzerFrameworksContent =>
      'Gigerenzer (2007): People using hierarchical decision frameworks possibly achieve goals 35% faster with 50% less effort.';

  @override
  String get compressionAdvantageContent =>
      'Shannon (1948): Hierarchical organization probably achieves optimal data compression. Applied to life experiences, this could allow processing exponentially more information.';

  @override
  String get applicationToMomentsTitle => 'Application to Moments:';

  @override
  String get compressionMomentsContent =>
      'Instead of remembering hundreds of disconnected experiences, hierarchical organization possibly allows compressing similar moments into categories, freeing mental resources for pattern recognition and future planning.';

  @override
  String get predictionMachineContent =>
      'Clark (2013): The brain possibly operates as a \"prediction machine,\" constantly generating models of future experiences based on past patterns.';

  @override
  String get neuralReductionLabel => 'Neural Reduction';

  @override
  String get predictableExperiencesSublabel => 'predictable experiences';

  @override
  String get unpredictedActivityLabel => 'Unpredicted Activity';

  @override
  String get neuralActivitySublabel => 'neural activity';

  @override
  String get organizedMomentTracking =>
      'Organized moment tracking probably creates better predictive models, reducing cognitive load and improving future decision-making accuracy.';

  @override
  String get entropyReductionContent =>
      'Bialek et al. (2001): Neural networks using hierarchical processing possibly achieve superior efficiency in information transmission compared to flat structures.';

  @override
  String get lifeApplicationEntropy =>
      'Life application: Hierarchical moment organization probably allows extracting maximum insight from experiences while minimizing cognitive noise.';

  @override
  String get creativityMythCounterTitle => 'Counter-evidence:';

  @override
  String get creativityMythCounterContent =>
      '• Stokes (2005): Creative professionals with organizational frameworks possibly produce more innovative work\n• Schwartz (2004): Too many unstructured options probably decrease creative output\n• Hierarchical organization probably reduces cognitive noise, freeing mental resources for creative thinking';

  @override
  String get successMythCounterContent =>
      '• Ericsson (2016): Elite performers in all domains probably use highly structured practice and reflection systems\n• High-performing individuals probably show superior organizational skills, not less structure\n• Successful hunter-gatherer societies probably had complex hierarchical organization systems';

  @override
  String get hierarchyMythCounterContent =>
      '• All successful primate societies probably exhibit hierarchical organization with clear roles\n• The human brain probably evolved hierarchical processing as its fundamental architecture\n• Even egalitarian societies possibly maintain hierarchical organization for different domains';

  @override
  String get simplicityMythCounterContent =>
      '• Appropriate complexity probably matches environmental demands\n• Over-simplification possibly leads to system failure\n• Well-structured complexity probably reduces cognitive load\n• Hierarchical organization probably achieves optimal balance between simplicity and information richness';

  @override
  String get anxietyMythCounterContent =>
      '• Unstructured uncertainty probably generates more anxiety than organized complexity\n• Clear frameworks possibly reduce decision anxiety\n• Hierarchical organization probably provides predictive structure that calms the nervous system\n• Studies suggest organizational clarity reduces stress hormones';

  @override
  String get fullPresentationMethodologyTitle => 'The Methodology';

  @override
  String get fullPresentationMethodologyContent =>
      'Our approach combines psychological insights with practical analysis:\n\n1. **Moment Identification**: Recognize key moments in your life\n2. **Dimensional Analysis**: Categorize across four levels\n3. **Directional Focus**: Understand inward vs outward orientation\n4. **Impact Assessment**: Evaluate motivation and satisfaction effects\n5. **Pattern Recognition**: Discover your latent values';

  @override
  String get fullPresentationFourLevelsTitle =>
      'Four Levels of Human Experience';

  @override
  String get fullPresentationFourLevelsContent =>
      'These levels work hierarchically, building upon each other to create your complete experience.';

  @override
  String get fullPresentationDirectionalFocusTitle => 'Directional Focus';

  @override
  String get fullPresentationDirectionalFocusContent =>
      'Every moment has both inward and outward components, but one direction typically dominates. Understanding this helps reveal your natural tendencies and preferences.';

  @override
  String get fullPresentationPracticalApplicationTitle =>
      'Practical Application';

  @override
  String get fullPresentationPracticalApplicationContent =>
      'Use this methodology to:\n\n• **Track patterns** in your decision-making\n• **Identify triggers** for motivation and satisfaction\n• **Understand conflicts** between different aspects of yourself\n• **Make better choices** aligned with your values\n• **Develop strategies** for personal growth';

  @override
  String get fullPresentationConclusionTitle => 'Your Journey Begins';

  @override
  String get fullPresentationConclusionContent =>
      'Now that you understand the methodology, you can:\n\n• Start recording your key moments\n• Analyze your patterns over time\n• Discover your unique latent values\n• Use insights for personal development\n\nRemember: Self-awareness is the first step toward intentional living.';

  @override
  String get fullPresentationElementalDescription =>
      'Physical and instinctual responses';

  @override
  String get fullPresentationPersonalDescription =>
      'Individual experiences and emotions';

  @override
  String get fullPresentationInformationalDescription =>
      'Intellectual and analytical processes';

  @override
  String get fullPresentationSocialDescription =>
      'Interpersonal and cultural connections';
}
