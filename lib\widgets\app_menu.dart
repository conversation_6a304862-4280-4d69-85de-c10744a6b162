import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:estimat_keymoments/l10n/app_localizations.dart';
import '../providers/locale_provider.dart';
import 'package:url_launcher/url_launcher.dart';

class AppMenu extends StatelessWidget {
  const AppMenu({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return PopupMenuButton<String>(
      icon: const Icon(Icons.menu),
      onSelected: (value) async {
        if (value == 'change_language') {
          _showLanguageDialog(context);
        } else if (value.startsWith('open_url:')) {
          final url = value.substring('open_url:'.length);
          await _launchUrl(url);
        } else {
          Navigator.pushNamed(context, value);
        }
      },
      itemBuilder: (context) => [
        PopupMenuItem(
          value: '/onboarding_presentation',
          child: Text(localizations.viewOnboardingMenu),
        ),
        PopupMenuItem(
          value: '/presentation',
          child: Text(localizations.viewFullPresentationMenu),
        ),
        PopupMenuItem(
          value: '/why_moments',
          child: Text(localizations.whyMomentsMenuTitle),
        ),
        PopupMenuItem(
          value: '/why_hierarchical',
          child: Text(localizations.whyHierarchicalMenuTitle),
        ),
        PopupMenuItem(
          value: '/level_process_focus',
          child: Text(localizations.viewLevelProcessFocusMenu),
        ),
        PopupMenuItem(
          value: '/latent_values',
          child: Text(localizations.viewLatentValuesMenu),
        ),
        PopupMenuItem(
          value: 'open_url:https://www.lesswrong.com/posts/j2htqhMoNN5E77FyP/from-ia-code-to-human-values-a-construction-from-maxent',
          child: Text(localizations.fromIACodeToHumanValuesMenu),
        ),
        PopupMenuItem(
          value: 'open_url:https://jacominesp.org.br',
          child: Text(localizations.jacoMinestSupportMenu),
        ),
        PopupMenuItem(
          value: 'change_language',
          child: Text(localizations.changeLanguageMenu),
        ),
      ],
    );
  }

  void _showLanguageDialog(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(localizations.selectLanguageTitle),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: Text(localizations.englishLanguage),
              onTap: () {
                localeProvider.setLocale(const Locale('en'));
                Navigator.pop(context);
              },
            ),
            ListTile(
              title: Text(localizations.spanishLanguage),
              onTap: () {
                localeProvider.setLocale(const Locale('es'));
                Navigator.pop(context);
              },
            ),
            ListTile(
              title: Text(localizations.portugueseLanguage),
              onTap: () {
                localeProvider.setLocale(const Locale('pt'));
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _launchUrl(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        throw 'Could not launch $url';
      }
    } catch (e) {
      // Handle error silently or show a snackbar if needed
      print('Error launching URL: $e');
    }
  }
}
