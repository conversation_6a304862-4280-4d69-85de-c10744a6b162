import 'package:flutter/material.dart';
import '../widgets/app_menu.dart';
import '../l10n/app_localizations.dart';

class LatentValuesScreen extends StatelessWidget {
  const LatentValuesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final locale = Localizations.localeOf(context);
    final languageCode = locale.languageCode;

    // Get translations for current language
    final levelTitles = _getLevelTitles(languageCode);
    final translations = _getTranslations(languageCode);
    final functions = translations['functions']!;
    final values = translations['values']!;
    final subtitles = translations['subtitles']!;

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.viewLatentValuesMenu),
        actions: const [
          AppMenu(),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Understanding explanation at the top
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20.0),
              margin: const EdgeInsets.only(bottom: 24.0),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    localizations.understandingLatentValuesTitle,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[800],
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    localizations.understandingLatentValuesContent,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[700],
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),

            // Header section
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20.0),
              margin: const EdgeInsets.only(bottom: 24.0),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.deepPurple.shade100, Colors.deepPurple.shade50],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.deepPurple.shade200),
              ),
              child: Row(
                children: [
                  Expanded(
                    flex: 1,
                    child: Text(
                      localizations.evolutiveFunctionsHeader,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey[700],
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: Text(
                      localizations.latentValuesSectionTitle,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey[700],
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Social Level
            _buildLevelSection(
              context,
              levelTitles['social']!,
              const Color(0xFF9F9F9F), // Social level color
              [
                _buildValueRow(
                  context,
                  '8',
                  functions['collaborate']!,
                  functions['negotiate']!,
                  values['collaborator']!,
                  subtitles['diplomat']!,
                  const Color(0xFF254110),
                ),
                _buildValueRow(
                  context,
                  '7',
                  functions['deliberate']!,
                  functions['empathize']!,
                  values['altruistic']!,
                  subtitles['empathetic']!,
                  const Color(0xFF0F5040),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Informational Level
            _buildLevelSection(
              context,
              levelTitles['informational']!,
              const Color(0xFF808080), // Informational level color
              [
                _buildValueRow(
                  context,
                  '6',
                  functions['predict']!,
                  functions['analyze']!,
                  values['strategist']!,
                  subtitles['analyst']!,
                  const Color(0xFF1E293B),
                ),
                _buildValueRow(
                  context,
                  '5',
                  functions['track']!,
                  functions['simplify']!,
                  values['tactician']!,
                  subtitles['synthesizer']!,
                  const Color(0xFF5E4E0F),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Personal Level
            _buildLevelSection(
              context,
              levelTitles['personal']!,
              const Color(0xFF595959), // Personal level color
              [
                _buildValueRow(
                  context,
                  '4',
                  functions['selfEnjoy']!,
                  functions['selfMotivate']!,
                  values['funny']!,
                  subtitles['enthusiastic']!,
                  const Color(0xFF794B19),
                ),
                _buildValueRow(
                  context,
                  '3',
                  functions['selfTransform']!,
                  functions['selfObserve']!,
                  values['versatile']!,
                  subtitles['selfSeer']!,
                  const Color(0xFF332444),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Elemental Level
            _buildLevelSection(
              context,
              levelTitles['elemental']!,
              const Color(0xFF404040), // Elemental level color
              [
                _buildValueRow(
                  context,
                  '2',
                  functions['execute']!,
                  functions['discard']!,
                  values['warrior']!,
                  subtitles['releaser']!,
                  const Color(0xFF5B1106),
                ),
                _buildValueRow(
                  context,
                  '1',
                  functions['store']!,
                  functions['recognize']!,
                  values['guardian']!,
                  subtitles['nurturer']!,
                  const Color(0xFF6A0F36),
                ),
              ],
            ),

          ],
        ),
      ),
    );
  }

  // Helper methods to get translations based on locale
  Map<String, String> _getLevelTitles(String languageCode) {
    switch (languageCode) {
      case 'es':
        return {
          'social': '4. Social',
          'informational': '3. Informacional',
          'personal': '2. Personal',
          'elemental': '1. Elemental',
        };
      case 'pt':
        return {
          'social': '4. Social',
          'informational': '3. Informacional',
          'personal': '2. Pessoal',
          'elemental': '1. Elemental',
        };
      default: // English
        return {
          'social': '4. Social',
          'informational': '3. Informational',
          'personal': '2. Personal',
          'elemental': '1. Elemental',
        };
    }
  }

  Map<String, Map<String, String>> _getTranslations(String languageCode) {
    switch (languageCode) {
      case 'es':
        return {
          'functions': {
            'collaborate': 'Colaborar',
            'negotiate': 'Negociar',
            'deliberate': 'Deliberar',
            'empathize': 'Empatizar',
            'predict': 'Predecir',
            'analyze': 'Analizar',
            'track': 'Rastrear',
            'simplify': 'Simplificar',
            'selfEnjoy': 'Auto-Disfrutar',
            'selfMotivate': 'Auto-Motivar',
            'selfTransform': 'Auto-Transformar',
            'selfObserve': 'Auto-Observar',
            'execute': 'Ejecutar',
            'discard': 'Descartar',
            'store': 'Almacenar',
            'recognize': 'Reconocer',
          },
          'values': {
            'collaborator': 'COLABORADOR',
            'altruistic': 'ALTRUISTA',
            'strategist': 'ESTRATEGA',
            'tactician': 'TÁCTICO',
            'funny': 'DIVERTIDO',
            'versatile': 'VERSÁTIL',
            'warrior': 'GUERRERO',
            'guardian': 'GUARDIÁN',
          },
          'subtitles': {
            'diplomat': 'Diplomático',
            'empathetic': 'Empático',
            'analyst': 'Analista',
            'synthesizer': 'Sintetizador',
            'enthusiastic': 'Entusiasta',
            'selfSeer': 'Auto-Observador',
            'releaser': 'Liberador',
            'nurturer': 'Cuidador',
          },
        };
      case 'pt':
        return {
          'functions': {
            'collaborate': 'Colaborar',
            'negotiate': 'Negociar',
            'deliberate': 'Deliberar',
            'empathize': 'Empatizar',
            'predict': 'Prever',
            'analyze': 'Analisar',
            'track': 'Rastrear',
            'simplify': 'Simplificar',
            'selfEnjoy': 'Auto-Desfrutar',
            'selfMotivate': 'Auto-Motivar',
            'selfTransform': 'Auto-Transformar',
            'selfObserve': 'Auto-Observar',
            'execute': 'Executar',
            'discard': 'Descartar',
            'store': 'Armazenar',
            'recognize': 'Reconhecer',
          },
          'values': {
            'collaborator': 'COLABORADOR',
            'altruistic': 'ALTRUÍSTA',
            'strategist': 'ESTRATEGISTA',
            'tactician': 'TÁTICO',
            'funny': 'DIVERTIDO',
            'versatile': 'VERSÁTIL',
            'warrior': 'GUERREIRO',
            'guardian': 'GUARDIÃO',
          },
          'subtitles': {
            'diplomat': 'Diplomata',
            'empathetic': 'Empático',
            'analyst': 'Analista',
            'synthesizer': 'Sintetizador',
            'enthusiastic': 'Entusiástico',
            'selfSeer': 'Auto-Observador',
            'releaser': 'Liberador',
            'nurturer': 'Cuidador',
          },
        };
      default: // English
        return {
          'functions': {
            'collaborate': 'Collaborate',
            'negotiate': 'Negotiate',
            'deliberate': 'Deliberate',
            'empathize': 'Empathize',
            'predict': 'Predict',
            'analyze': 'Analyze',
            'track': 'Track',
            'simplify': 'Simplify',
            'selfEnjoy': 'Self-Enjoy',
            'selfMotivate': 'Self-Motivate',
            'selfTransform': 'Self-Transform',
            'selfObserve': 'Self-Observe',
            'execute': 'Execute',
            'discard': 'Discard',
            'store': 'Store',
            'recognize': 'Recognize',
          },
          'values': {
            'collaborator': 'COLLABORATOR',
            'altruistic': 'ALTRUISTIC',
            'strategist': 'STRATEGIST',
            'tactician': 'TACTICIAN',
            'funny': 'FUNNY',
            'versatile': 'VERSATILE',
            'warrior': 'WARRIOR',
            'guardian': 'GUARDIAN',
          },
          'subtitles': {
            'diplomat': 'Diplomat',
            'empathetic': 'Empathetic',
            'analyst': 'Analyst',
            'synthesizer': 'Synthesizer',
            'enthusiastic': 'Enthusiastic',
            'selfSeer': 'SelfSeer',
            'releaser': 'Releaser',
            'nurturer': 'Nurturer',
          },
        };
    }
  }

  Widget _buildLevelSection(
    BuildContext context,
    String levelTitle,
    Color levelColor,
    List<Widget> valueRows,
  ) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        children: [
          // Level header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              color: levelColor,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Text(
              levelTitle,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          // Value rows
          ...valueRows,
        ],
      ),
    );
  }

  Widget _buildValueRow(
    BuildContext context,
    String number,
    String function,
    String subFunction,
    String latentValue,
    String latentSubtitle,
    Color latentColor,
  ) {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200, width: 1),
        ),
      ),
      child: Row(
        children: [
          // Left side - Evolutive Functions
          Expanded(
            flex: 1,
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                border: Border(
                  right: BorderSide(color: Colors.grey.shade300, width: 1),
                ),
              ),
              child: GestureDetector(
                onTap: () => _showValueDescription(context, latentValue),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: latentColor,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Center(
                        child: Text(
                          number,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            subFunction,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            function,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: latentColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Icon(
                      Icons.info_outline,
                      color: Colors.grey[600],
                      size: 20,
                    ),
                  ],
                ),
              ),
            ),
          ),
          // Right side - Latent Value
          Expanded(
            flex: 1,
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: latentColor,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    latentSubtitle,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.white.withValues(alpha: 0.8),
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    latentValue,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 1.2,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showValueDescription(BuildContext context, String latentValue) {
    String description;

    switch (latentValue) {
      case 'GUARDIÃO':
        description = 'Valor focado em preservar, proteger e manter estabilidade. Representa a capacidade de reconhecer e armazenar informações importantes para segurança e continuidade.';
        break;
      case 'GUERREIRO':
        description = 'Valor focado em ação, execução e superação de obstáculos. Representa a capacidade de executar decisões e descartar o que não serve mais.';
        break;
      case 'VERSÁTIL':
        description = 'Valor focado em adaptabilidade e auto-observação. Representa a capacidade de se transformar e observar a si mesmo para crescimento pessoal.';
        break;
      case 'DIVERTIDO':
        description = 'Valor focado em prazer, motivação e energia positiva. Representa a capacidade de auto-desfrutar e auto-motivar para manter o bem-estar.';
        break;
      case 'ESTRATEGISTA':
        description = 'Valor focado em planejamento e análise profunda. Representa a capacidade de prever cenários e analisar informações complexas.';
        break;
      case 'TÁTICO':
        description = 'Valor focado em síntese e rastreamento eficiente. Representa a capacidade de simplificar complexidades e rastrear progresso.';
        break;
      case 'ALTRUÍSTA':
        description = 'Valor focado em empatia e consideração pelos outros. Representa a capacidade de deliberar e empatizar para o bem comum.';
        break;
      case 'COLABORADOR':
        description = 'Valor focado em cooperação e negociação. Representa a capacidade de colaborar efetivamente e negociar soluções mutuamente benéficas.';
        break;
      default:
        description = 'Descrição não disponível.';
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            latentValue,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 20,
            ),
          ),
          content: SingleChildScrollView(
            child: Text(
              description,
              style: const TextStyle(
                fontSize: 16,
                height: 1.5,
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('Fechar'),
            ),
          ],
        );
      },
    );
  }
}
