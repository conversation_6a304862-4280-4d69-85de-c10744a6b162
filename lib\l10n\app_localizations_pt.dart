// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Portuguese (`pt`).
class AppLocalizationsPt extends AppLocalizations {
  AppLocalizationsPt([String locale = 'pt']) : super(locale);

  @override
  String get appTitle => 'Estimat KeyMoments';

  @override
  String get selectLanguageTitle => 'Selecionar Idioma';

  @override
  String get englishLanguage => 'Inglês';

  @override
  String get portugueseLanguage => 'Português';

  @override
  String get spanishLanguage => 'Espanhol';

  @override
  String get onboardingScreenTitle => 'Bem-vindo ao Estimat KeyMoments';

  @override
  String get onboardingScene1Message =>
      'O ruído pode ser simplificado em notas e transformado em música';

  @override
  String get onboardingScene1MessagePart2 => 'Com um pouco de proporção...';

  @override
  String get onboardingScene2Message =>
      'Qualquer imagem pode ser replicada através de luz, sombra e cor, e transformada em arte';

  @override
  String get onboardingScene2MessagePart2 => 'Com um pouco de proporção...';

  @override
  String get onboardingScene3Message =>
      'Sua sobrecarga cognitiva—você poderia refinar o que mais valoriza em si mesmo?';

  @override
  String get onboardingScene3MessagePart2 => 'Com um pouco de ...';

  @override
  String get onboardingScene4Message =>
      'Com a teoria da informação, uma IA pode reduzir dados complexos a 5% e reconstruí-los com 95% de fidelidade funcional; estes são vetores latentes';

  @override
  String get onboardingScene4MessagePart2 =>
      'Talvez você pudesse aplicar um pouco disso aos seus valores latentes...';

  @override
  String get onboardingFlowchartMessage =>
      'Vamos identificar nossos momentos de referência. Rastrear os fluxos associados a esses momentos. Avaliar de acordo com sua função. Descobrir os valores latentes que nos impulsionam:';

  @override
  String get onboardingFlowchartTitle => 'Fluxo do Processo';

  @override
  String get nextButton => 'Próximo';

  @override
  String get previousButton => 'Anterior';

  @override
  String get getStartedButton => 'Começar';

  @override
  String get exitToAppButton => 'Sair do aplicativo';

  @override
  String get motivationAppBarTitle => 'Momentos de Motivação';

  @override
  String get satisfactionAppBarTitle => 'Momentos de Satisfação';

  @override
  String get selectMomentTypeTitle => 'Selecionar Tipo de Momento';

  @override
  String get selectImprovementWorsening => 'Escolha entre Melhoria ou Piora:';

  @override
  String get selectMotivationSatisfaction =>
      'Escolha entre Motivação ou Satisfação:';

  @override
  String get improvesLabel => 'Melhora';

  @override
  String get worsensLabel => 'Piora';

  @override
  String get motivationLabel => 'Motivação';

  @override
  String get satisfactionLabel => 'Satisfação';

  @override
  String get elementalLabel => 'Elemental';

  @override
  String get personalLabel => 'Pessoal';

  @override
  String get informationalLabel => 'Informacional';

  @override
  String get socialLabel => 'Social';

  @override
  String get elementalExplanation =>
      'Com base em estímulos ambientais, damos uma resposta que foi modulada pela evolução. Mais correlacionado com atividades físicas.';

  @override
  String get personalExplanation =>
      'Somando-se à memória que vem de nossos ancestrais, temos uma memória que é criada a partir da interação única entre um indivíduo e seu ambiente. Quanto mais dolorosas, mais adaptativas para a sobrevivência. Quanto mais prazerosas, mais motivadoras para a sobrevivência. Mais correlacionado com hobbies, gostos, autoconhecimento.';

  @override
  String get informationalExplanation =>
      'Ao atingir o nível intelectual, além de integrar memórias e experiências com o ambiente, alcançamos uma capacidade única: relacionar memórias ou memórias próprias de um indivíduo entre si e codificar informações abstratas (exemplo: sinais matemáticos ou de linguagem).';

  @override
  String get socialExplanation =>
      'As relações sociais envolvem a conexão de informações entre indivíduos, a troca de cultura e comunicação. Podemos definir um limite entre nível intelectual e nível social quando usamos nossos processos intelectuais para interagir com outros seres capazes de combinar suas próprias memórias.';

  @override
  String get inwardLabel => 'Interior';

  @override
  String get outwardLabel => 'Exterior';

  @override
  String get inwardExplanation =>
      'Reflexivo: Para armazenar, auto-transformar, prever, empatizar.';

  @override
  String get outwardExplanation =>
      'Intuitivo: Para executar, desfrutar, rastrear, colaborar.';

  @override
  String get titleInputHint => 'Digite um título de duas palavras';

  @override
  String get descriptionInputHint => 'Descreva este momento';

  @override
  String get evidenceInputHint => 'Forneça evidências para esta distribuição';

  @override
  String get describeDistributionHint =>
      'Descreva por que você escolheu esta distribuição...';

  @override
  String get firstMomentEvidenceHint =>
      'Explique por que este momento cria mais possibilidades do que simplesmente estar vivo. Que novas oportunidades, caminhos ou potencial ele abre?';

  @override
  String get comparisonEvidenceHint =>
      'Explique quantas possibilidades este momento cria comparado à sua linha de base de vida...';

  @override
  String get lifePossibilitiesFactorTitle => 'Fator de Possibilidades de Vida';

  @override
  String get howManyPossibilitiesTitle =>
      'Quantas Possibilidades Este Momento Cria?';

  @override
  String get comparedToLifeBaseline =>
      'Comparado à sua linha de base de vida, este momento cria:';

  @override
  String get morePossibilitiesButton => 'Mais Possibilidades';

  @override
  String get fewerPossibilitiesButton => 'Menos Possibilidades';

  @override
  String get vsLifeBaseline => 'vs Linha de Base de Vida';

  @override
  String get explainWhyFirstMomentTitle =>
      'Explique Por Que Este Momento Cria Mais Possibilidades Que a Vida';

  @override
  String get provideEvidenceTitle =>
      'Forneça Evidências para esta Avaliação de Possibilidades';

  @override
  String get continueButton => 'Continuar';

  @override
  String get lifePossibilitiesChart => 'Gráfico de Possibilidades de Vida';

  @override
  String get allTimeFilter => 'Todo o Tempo';

  @override
  String get lastWeekFilter => 'Última Semana';

  @override
  String get lastMonthFilter => 'Último Mês';

  @override
  String get last3MonthsFilter => 'Últimos 3 Meses';

  @override
  String get currentPreviewLabel => 'Visualização Atual:';

  @override
  String get lifeLabel => 'Vida';

  @override
  String get previewLabel => 'Visualização';

  @override
  String get lifeBaselineLabel => 'Linha de Base de Vida (1x)';

  @override
  String get morePossibilitiesLabel => 'Mais Possibilidades';

  @override
  String get fewerPossibilitiesLabel => 'Menos Possibilidades';

  @override
  String get currentPreviewLegend => 'Visualização Atual';

  @override
  String get lifePossibilitiesExplanation =>
      'Compare este momento à sua linha de base de vida (1x). Quantas vezes mais possibilidades este momento cria para você? Você pode ir acima ou abaixo de momentos anteriores, mas nunca abaixo da linha de base de vida.\\n\\nO cálculo fatorial representa o crescimento exponencial de possibilidades que momentos significativos podem criar em sua vida.';

  @override
  String get minimumLifeBaselineNote => 'Mínimo: 1.0x (linha de base de vida)';

  @override
  String get guardianLabel => 'Guardião';

  @override
  String get warriorLabel => 'Guerreiro';

  @override
  String get versatileLabel => 'Versátil';

  @override
  String get funLabel => 'Divertido';

  @override
  String get strategistLabel => 'Estrategista';

  @override
  String get tacticalLabel => 'Tático';

  @override
  String get altruistLabel => 'Altruísta';

  @override
  String get collaboratorLabel => 'Colaborador';

  @override
  String get summaryTitle => 'Resumo';

  @override
  String get motivationSectionTitle => 'Análise de Motivação';

  @override
  String get satisfactionSectionTitle => 'Análise de Satisfação';

  @override
  String get latentValuesSectionTitle => 'Valores Latentes';

  @override
  String get improvesMotivationLabel => 'Melhora Motivação';

  @override
  String get worsensMotivationLabel => 'Piora Motivação';

  @override
  String get improvesSatisfactionLabel => 'Melhora Satisfação';

  @override
  String get worsensSatisfactionLabel => 'Piora Satisfação';

  @override
  String get proportionLabel => 'Proporção';

  @override
  String get exportDataButton => 'Exportar Dados';

  @override
  String get viewOnboardingMenu => 'Introdução';

  @override
  String get viewFullPresentationMenu => 'Apresentação Completa';

  @override
  String get viewLevelProcessFocusMenu => 'Níveis e Direções';

  @override
  String get viewLatentValuesMenu => 'Funções Evolutivas e Valores';

  @override
  String get fromIACodeToHumanValuesMenu => 'Do código IA aos valores humanos';

  @override
  String get jacoMinestSupportMenu => 'Apoio Jacominest';

  @override
  String get changeLanguageMenu => 'Mudar Idioma';

  @override
  String get saveButton => 'Salvar';

  @override
  String get cancelButton => 'Cancelar';

  @override
  String get backButton => 'Voltar';

  @override
  String get whyMomentsScreenTitle => 'Por que Momentos?';

  @override
  String get whyMomentsMenuTitle => 'Por que Momentos?';

  @override
  String get whyHierarchicalMenuTitle => 'Por que Organização Hierárquica?';

  @override
  String get whyHierarchicalScreenTitle => 'Por que Organização Hierárquica?';

  @override
  String get levelsAndDirectionsScreenTitle => 'Níveis e Direções';

  @override
  String get skipForNowButton => 'Pular por enquanto';

  @override
  String get noMomentsRecordedYet => 'Nenhum momento registrado ainda.';

  @override
  String pageCounter(int current, int total) {
    return '$current / $total';
  }

  @override
  String get welcomeToEstimatKeyMoments => 'Bem-vindo ao Estimat KeyMoments';

  @override
  String get estimatKeyMomentsDescription =>
      'Uma metodologia abrangente para compreender e analisar os momentos-chave que moldam suas decisões de vida e crescimento pessoal.';

  @override
  String get editThisMomentButton => 'Editar este momento';

  @override
  String get closeButton => 'Fechar';

  @override
  String get insertNewMomentButton => 'Inserir um novo momento';

  @override
  String get viewAllMomentsButton => 'Ver Todos os Momentos';

  @override
  String get momentsHistoryTitle => 'Histórico de Momentos';

  @override
  String momentSavedTitle(String momentType) {
    return '$momentType Salvo';
  }

  @override
  String get momentSavedMessage => 'Seu momento foi salvo com sucesso.';

  @override
  String factorialComparisonLabel(String comparisonType) {
    return 'Comparação Fatorial: $comparisonType';
  }

  @override
  String get improvementLabel => 'Melhoria';

  @override
  String get worseningLabel => 'Piora';

  @override
  String factorialSliderValueLabel(String value) {
    return 'Valor do Controle Deslizante Fatorial: $value';
  }

  @override
  String factorialMultiplierLabel(String value) {
    return 'Multiplicador Fatorial: $value';
  }

  @override
  String get whyRegisterMomentsTitle =>
      'Por que importa registrar momentos mais objetivamente?';

  @override
  String get whyRegisterMomentsContent =>
      'Seus picos e vales emocionais não são apenas dados soltos: são como as coordenadas de sua bússola interna. Registrar sua motivação (o quão impulsionado você se sente) e sua satisfação (o quão pleno você se sente) lhe dá um mapa claro para planejar metas, rotinas ou tarefas que realmente somem à sua vida. Isso se torna fundamental quando você descobre onde essas duas coisas se desalinham, porque aí você pode ajustar e projetar atividades que funcionem melhor para você.';

  @override
  String get whenNoticeMoreTitle => 'Quando isso é mais perceptível?';

  @override
  String get whenNoticeMoreContent =>
      'Quando você está em baixa emocional. Nesses momentos, seu cérebro o engana com pensamentos como: \"Nunca consigo fazer nada direito.\" \"Não há nada que me motive.\" Embora isso não seja verdade, o viés de memória—essa tendência de lembrar o negativo ou o mais recente—faz você se sentir assim. 📈 Manter um registro objetivo de seus momentos altos lhe dá provas reais de quem você é quando está bem, para que não se perca na névoa quando está mal.';

  @override
  String get highMotivationSatisfactionTitle =>
      'Alta motivação e alta satisfação';

  @override
  String get highMotivationSatisfactionContent =>
      'Pergunta rápida: O que foi mais significativo que aconteceu com você este mês? Se você tivesse um registro diário de suas emoções, responderia o mesmo? Provavelmente não. E há uma razão: Viés de memória: Segundo Kahneman e Tversky (1979), nosso cérebro tende a dar mais peso ao que é recente ou intenso (isso é chamado de regra do pico-fim). Por isso, às vezes esquecemos momentos valiosos que não foram tão \"barulhentos\". Solução: Anotar seus picos compensa essa armadilha mental e mostra o panorama completo.';

  @override
  String get evolutionaryViewTitle => 'Perspectiva evolutiva';

  @override
  String get evolutionaryViewContent =>
      'Emoções positivas fortes—como orgulho ou plenitude—são sinais de oportunidades: um relacionamento que funciona, uma conquista pessoal ou uma decisão que o alinha com o que você quer. Nossos ancestrais sobreviviam melhor se lembrassem desses momentos para repeti-los. Seu cérebro não foi projetado apenas para fazê-lo feliz, mas para ajudá-lo a prosperar. Se você sabe o que o motiva, pode buscá-lo com mais frequência.';

  @override
  String get practiceEstimatTitle => 'Prática com ESTIMAT:';

  @override
  String get practiceEstimatContent =>
      'Imagine que você anota: ✍️ \"Me senti poderoso explicando minhas emoções em um workshop sem ser julgado.\" 🔁 Ao revisar várias entradas similares, ESTIMAT mostra: Alta motivação quando você expressa o que sente. Alta satisfação com escuta ativa e validação. 🧭 O que fazer com isso? Procure ou crie mais situações assim (workshops, conversas com amigos compreensivos). Use como âncora quando se sentir perdido.';

  @override
  String get highMotivationLowSatisfactionTitle =>
      'Alta Motivação + Baixa Satisfação';

  @override
  String get highMotivationLowSatisfactionContent =>
      'Já aconteceu de você ficar muito empolgado com algo e depois sentir um \"meh\"? Exemplo: Você sonha com o novo iPhone 📱, corre para comprá-lo e no dia seguinte pensa: \"Era só isso?\" Viés de impacto: Segundo Gilbert e Wilson, tendemos a superestimar a felicidade futura em 40-60%. É como se seu cérebro colocasse um filtro do Instagram no que você espera, para que você se mova em direção a isso. Realidade: Quando o momento chega, nem sempre está à altura do hype.';

  @override
  String get lowMotivationHighSatisfactionTitle =>
      'Baixa Motivação + Alta Satisfação';

  @override
  String get lowMotivationHighSatisfactionContent =>
      'Já se arrastou para fazer algo e acabou surpreso com o quão bem se sentiu? ⛈️😊 Exemplo: Você não quer ir à academia, mas sai com um sorriso. Subestimação: Estudos da Universidade de British Columbia mostram que subestimamos o prazer do exercício em 20-30%. Você pensa que será +1, mas acaba sendo +3. Paradoxo do esforço: O esforço em si lhe dá um bônus de satisfação—cada passo conta.';

  @override
  String get lowMotivationLowSatisfactionTitle =>
      'Baixa Motivação + Baixa Satisfação';

  @override
  String get lowMotivationLowSatisfactionContent =>
      'E aqueles dias em que não há desejo nem prazer? 😔 Você poderia ignorá-los ou se forçar a continuar, mas e se essas baixas fossem como sementes para algo maior? Refletir sobre esses momentos difíceis não apenas ajuda você a sair do buraco, mas pode ensinar como evitar cair tão fundo da próxima vez e até acender novas ideias. Um estudo de 2020 na Frontiers in Psychology explorou como pensar profundamente sobre problemas pode ajudar quando você está em baixa. Descobriram que pessoas que refletiam sobre seus problemas, buscando soluções, se sentiam menos deprimidas após algumas semanas.';

  @override
  String get evolutiveFunctionsHeader => 'Funções Evolutivas';

  @override
  String get valueDescriptionGuardian =>
      'Reconhecer: Você reconhece, consome, ingere.\nArmazenar: Você descansa, armazena e metaboliza.';

  @override
  String get valueDescriptionWarrior =>
      'Descartar: Você descarta, foge, inibe.\nExecutar: Você luta, ataca e se esforça.';

  @override
  String get valueDescriptionVersatile =>
      'Observar-se: Você destaca informações negativas, sente dor, observa erros.\nTransformar-se: Você reduz seus erros e adapta estímulos.';

  @override
  String get valueDescriptionFunny =>
      'Motivar-se: Você destaca informações positivas, sente prazer.\nDisfrutar-se: Você aprimora sucessos e contrasta atitudes e ideias.';

  @override
  String get valueDescriptionStrategist =>
      'Analisar: Você revisa tendências, pergunta se algo pode ser falso, analisa.\nPrever: Você prevê o que é mais provável e cria hipóteses.';

  @override
  String get valueDescriptionTactician =>
      'Simplificar: Você generaliza, compara a maneira mais fácil e rápida.\nRastrear: Você busca, caça e rastreia.';

  @override
  String get valueDescriptionAltruistic =>
      'Empatizar: Você empatiza com o que é importante para os outros.\nDeliberar: Considera a si mesmo e às necessidades do maior número possível de pessoas, praticando o altruísmo eficiente.';

  @override
  String get valueDescriptionCollaborator =>
      'Negociar: Você ajuda a entender, comunicar.\nColaborar: Você coopera em direção a objetivos compartilhados.';

  @override
  String get latentValuesTitle => 'Latent Values';

  @override
  String get lifePossibilitiesChartTitle => 'Life Possibilities Chart';

  @override
  String get periodLabel => 'Period:';

  @override
  String get last7DaysFilter => 'Last 7 Days';

  @override
  String get customRangeFilter => 'Custom Range';

  @override
  String get customDateRangeFilterTitle => 'Custom Date Range Filter';

  @override
  String get startDateLabel => 'Start Date:';

  @override
  String get endDateLabel => 'End Date:';

  @override
  String get selectStartDateHint => 'Select start date';

  @override
  String get selectEndDateHint => 'Select end date';

  @override
  String get resetButton => 'Reset';

  @override
  String get customRangeActiveLabel => 'Custom range active';

  @override
  String showingMomentsLabel(int count, String plural) {
    return 'Showing $count moment$plural';
  }

  @override
  String get whyFocusOnMomentsTitle => 'Por que Focar em Momentos?';

  @override
  String get whyFocusOnMomentsContent =>
      'Momentos-chave são os blocos de construção do nosso processo de tomada de decisões. Ao compreender esses momentos, podemos:\n\n• Identificar padrões em nosso comportamento\n• Entender o que realmente nos motiva\n• Reconhecer o que nos traz satisfação\n• Fazer escolhas mais conscientes\n• Desenvolver melhor autoconhecimento';

  @override
  String get discoveringLatentValuesTitle => 'Descobrindo Valores Latentes';

  @override
  String discoveringLatentValuesContent(
    String guardianLabel,
    String warriorLabel,
    String versatileLabel,
    String funLabel,
  ) {
    return 'Seus valores latentes emergem da combinação de:\n\n• **Porcentagens de nível** (como você distribui entre os quatro níveis)\n• **Foco direcional** (orientação interna vs externa)\n• **Tipo de impacto** (melhora vs piora)\n\nEstes valores revelam suas forças centrais: $guardianLabel, $warriorLabel, $versatileLabel, $funLabel, e outros.';
  }

  @override
  String get understandingLatentValuesTitle => 'Compreendendo Valores Latentes';

  @override
  String get understandingLatentValuesContent =>
      'Seus valores latentes emergem de como você distribui seu foco entre os quatro níveis hierárquicos e se você tende para uma orientação interna ou externa. Cada combinação revela forças centrais e tendências naturais que orientam sua tomada de decisões e satisfação na vida.';

  @override
  String get directionsFocusTitle => 'Foco das Direções';

  @override
  String get whyHierarchicalOrganizationTitle =>
      'Why Hierarchical Organization of Life Moments Might Matter';

  @override
  String get dataVisualizationBiasReductionTitle =>
      'Visualização de Dados e Redução de Viés';

  @override
  String get experimentalEvidenceTitle => 'Evidência Experimental';

  @override
  String get evolutionaryPsychologyPerspectiveTitle =>
      'Perspectiva de Psicologia Evolutiva';

  @override
  String get recognizeYourMomentOrientationLevel =>
      'Reconheça seu momento, sua orientação e nível';

  @override
  String get recognizeYourMoment => 'Reconheça seu momento';

  @override
  String get organizeTheMomentAndLookForEvidence =>
      'Agora vamos olhar a distribuição de informação no momento e procurar evidências.';

  @override
  String get orientationTitle => 'Orientação';

  @override
  String get orientationQuestion =>
      'Seu momento foi mais focado em mudar internamente ou externamente?';

  @override
  String get threeLevelsTitle => 'Níveis';

  @override
  String get threeLevelsDescription =>
      'Análise hierárquica do seu momento através dos quatro níveis de experiência humana';

  @override
  String get pieStepElementalPersonalTitle => '3 Níveis: Elementar vs Pessoal';

  @override
  String get pieStepElementalPersonalDescription =>
      'Para o seu momento, você se concentrou mais em seu estado corporal ou em seus interesses/emoções pessoais?';

  @override
  String get pieStepPersonalInformationalTitle =>
      '3 Níveis: Pessoal vs Informacional';

  @override
  String get pieStepPersonalInformationalDescription =>
      'Para o seu momento, você se concentrou mais em seus interesses/emoções pessoais ou em recolher e processar informações?';

  @override
  String get pieStepInformationalSocialTitle =>
      '3 Níveis: Informacional vs Social';

  @override
  String get pieStepInformationalSocialDescription =>
      'Para o seu momento, você se concentrou mais em analisar informações ou em se conectar com outros?';

  @override
  String get pieStepEvidenceQuestion =>
      'Quais evidências ou variáveis influenciaram sua escolha?';

  @override
  String get whyMomentsHeaderSubtitle =>
      'Analise seus momentos, conheça seus padrões';

  @override
  String get whyRegisterMomentsObjectivelyTitle =>
      'Por que registrar momentos objetivamente?';

  @override
  String get whyRegisterMomentsObjectivelyContent =>
      'Seus picos e vales emocionais não são apenas dados soltos: são como as coordenadas de sua bússola interna. Registrar sua motivação (o quão impulsionado você se sente) e sua satisfação (o quão pleno você se sente) lhe dá um mapa claro para planejar metas, rotinas ou tarefas que realmente somem à sua vida.';

  @override
  String get whyRegisterMomentsObjectivelyHighlight =>
      '📈 Manter um registro de seus picos de motivação e satisfação serve para compensar a distorção emocional. Permite que você tenha evidências de quem você é quando está bem, para não se perder de vista quando não está.';

  @override
  String get highMotivationHighSatisfactionTitle =>
      'Alta Motivação + Alta Satisfação';

  @override
  String get researchInfoBoxTitle => '🧠 Pesquisa';

  @override
  String get researchInfoBoxContent =>
      'Segundo Kahneman e Tversky (1979), nosso cérebro tende a dar mais peso ao que é recente ou intenso (regra do pico-fim). Por isso, às vezes esquecemos momentos valiosos que não foram tão \"barulhentos\".';

  @override
  String get evolutionaryViewInfoBoxTitle => '🔬 Visão Evolutiva';

  @override
  String get evolutionaryViewInfoBoxContent =>
      'Emoções positivas fortes sinalizam oportunidades adaptativas: relacionamentos bem-sucedidos, decisões alinhadas ao propósito, conquistas sociais ou pessoais.';

  @override
  String get practiceEstimatInfoBoxTitle => '✍️ Prática com ESTIMAT';

  @override
  String get practiceEstimatInfoBoxContent =>
      'Ao revisar várias entradas, ESTIMAT mostra padrões e ajuda você a replicar esses momentos de alta motivação e satisfação.';

  @override
  String get highMotivationLowSatisfactionIntro =>
      'Já aconteceu de você ficar muito empolgado com algo e depois sentir um \"meh\"? 📱';

  @override
  String get impactBiasInfoBoxTitle => '📊 Viés de Impacto';

  @override
  String get impactBiasInfoBoxContent =>
      'Segundo Gilbert e Wilson, tendemos a superestimar a felicidade futura em';

  @override
  String get practiceInfoBoxTitle => '🎯 Prática';

  @override
  String get practiceInfoBoxContent =>
      '• Simule: Antes de se lançar, imagine cores, cheiros, sons\n• Previsão vs. realidade: Anote quanto você acha que vai gostar\n• Ajuste: Se esperava +3 e foi +1, repense se vale a pena repetir';

  @override
  String get lowMotivationHighSatisfactionIntro =>
      'Já se arrastou para fazer algo e acabou surpreso com o quão bem se sentiu? ⛈️😊';

  @override
  String get pleasureUnderestimationInfoBoxTitle =>
      '🏃‍♂️ Subestimação do Prazer';

  @override
  String get pleasureUnderestimationInfoBoxContent =>
      'A Universidade de British Columbia mostrou que as pessoas subestimam seu prazer no exercício em';

  @override
  String get effortParadoxInfoBoxTitle => '🧬 Paradoxo do Esforço';

  @override
  String get effortParadoxInfoBoxContent =>
      'Cada gota de esforço é trocada por um bônus extra de satisfação. Essa \"explosão de alegria\" é seu cérebro dizendo \"Bem feito!\"';

  @override
  String get lowMotivationLowSatisfactionIntro =>
      'E aqueles dias em que não há desejo nem prazer? 😔 Essas baixas podem ser sementes para suas melhores ideias.';

  @override
  String get reflectionPowerInfoBoxTitle => '📈 Poder da Reflexão';

  @override
  String get reflectionPowerInfoBoxContent =>
      'Estudantes que praticaram autoavaliação reflexiva mostraram um aumento de';

  @override
  String get practiceEstimatLowInfoBoxTitle => '🎯 Prática com ESTIMAT';

  @override
  String get practiceEstimatLowInfoBoxContent =>
      '• Registre sua baixa: Escreva sem filtros como se sente\n• Revise seu registro: ESTIMAT mostrará padrões\n• Faça algo pequeno: Uma caminhada, uma música, três gratidões';

  @override
  String get generalOverviewTitle => 'O Panorama Geral';

  @override
  String get generalOverviewIntro =>
      'Registrar suas emoções não é apenas um passatempo—é uma ferramenta para se conhecer e aproveitar ao máximo seu cérebro.';

  @override
  String get memoryBiasStatTitle => '🧠 Viés de memória';

  @override
  String get memoryBiasStatContent =>
      'Supervalorizamos o que é recente ou intenso';

  @override
  String get impactBiasStatTitle => '🎯 Viés de impacto';

  @override
  String get impactBiasStatContent => 'Superestimamos a felicidade futura';

  @override
  String get underestimationStatTitle => '💪 Subestimação';

  @override
  String get underestimationStatContent =>
      'Gostamos do exercício mais do que pensamos';

  @override
  String get recoveryStatTitle => '🔄 Recuperação';

  @override
  String get recoveryStatContent => 'Refletir te dá mais dias motivados';

  @override
  String get generalOverviewConclusion =>
      'Que pequeno passo você pode dar hoje para entender melhor suas emoções? Tente anotar um pico e um vale—os resultados podem te surpreender. ✨';

  @override
  String get whyHierarchicalHeaderSubtitle =>
      'Por que a Organização Hierárquica de Momentos de Vida Pode Importar';

  @override
  String get whyHierarchicalImportantNote =>
      'Importante: Se você já está experimentando bem-estar consistente e satisfação profunda, organização adicional provavelmente é desnecessária. Esta abordagem parece mais relevante durante transições, decisões complexas ou insatisfação persistente.';

  @override
  String get informationTheoryPerspectiveTitle =>
      'Perspectiva de Teoria da Informação';

  @override
  String get debunkingCommonMythsTitle => 'Desmistificando Mitos Comuns';

  @override
  String get selfPerceptionBiasesTitle =>
      'O Problema dos Vieses de Autopercepção';

  @override
  String get visualProportionsTitle => 'Vantagens das Proporções Visuais';

  @override
  String get statsVsIntuitionTitle => 'Estatísticas Pessoais vs. Intuição';

  @override
  String get memoryHierarchyTitle => 'Experimentos de Hierarquia de Memória';

  @override
  String get decisionFatigueTitle => 'Estudos de Fadiga de Decisão';

  @override
  String get millerNumberTitle => 'O Número Mágico de Miller';

  @override
  String get availabilityBiasContent =>
      'Viés de Disponibilidade (Tversky & Kahneman, 1973): É provável que lembremos desproporcionalmente de eventos recentes ou emocionais, o que pode distorcer nossa percepção dos padrões de vida.';

  @override
  String get overestimationLabel => 'Superestimação';

  @override
  String get overestimationSublabel => 'de padrões recentes vs. reais';

  @override
  String get decisionDistortionLabel => 'Distorção de Decisão';

  @override
  String get decisionDistortionSublabel => 'de escolhas baseadas em memória';

  @override
  String get hierarchicalVisualizationNote =>
      'A visualização hierárquica de momentos pode neutralizar esse viés fornecendo uma representação mais objetiva dos padrões temporais.';

  @override
  String get clevelandMcGillContent =>
      'Cleveland & McGill (1984): A percepção visual de proporções parece ser significativamente mais precisa que memórias narrativas para avaliar distribuições temporais.';

  @override
  String get potentialPersonalApplicationsTitle =>
      'Aplicações Pessoais Potenciais:';

  @override
  String get personalApplicationsList =>
      '• Distribuição real de tempo vs. percepção\n• Padrões de energia e estados emocionais\n• Frequência de diferentes tipos de experiência\n• Progresso em direção a objetivos de longo prazo';

  @override
  String get visualizationDiscrepanciesNote =>
      'Essas visualizações podem revelar discrepâncias entre percepção subjetiva e realidade objetiva, facilitando decisões mais informadas.';

  @override
  String get personalIntuitionParadoxContent =>
      'Paradoxo da Intuição Pessoal: Embora confiemos em nossa intuição para decisões pessoais, é provável que apliquemos análise estatística rigorosa para decisões profissionais ou financeiras.';

  @override
  String get financialDecisionsLabel => 'Decisões Financeiras';

  @override
  String get financialDecisionsSublabel => 'usam dados objetivos';

  @override
  String get personalDecisionsLabel => 'Decisões Pessoais';

  @override
  String get personalDecisionsSublabel => 'usam dados objetivos';

  @override
  String get potentialImprovementLabel => 'Melhoria Potencial';

  @override
  String get potentialImprovementSublabel => 'com organização sistemática';

  @override
  String get hierarchicalAnalyticalNote =>
      'A organização hierárquica pode permitir aplicar rigor analítico às decisões de vida mantendo flexibilidade emocional e intuitiva.';

  @override
  String get socialLevelTitle => '4. Social';

  @override
  String get informationalLevelTitle => '3. Informacional';

  @override
  String get personalLevelTitle => '2. Pessoal';

  @override
  String get elementalLevelTitle => '1. Elemental';

  @override
  String get collaborateFunction => 'Colaborar';

  @override
  String get negotiateFunction => 'Negociar';

  @override
  String get collaboratorValue => 'COLABORADOR';

  @override
  String get diplomatSubtitle => 'Diplomata';

  @override
  String get deliberateFunction => 'Deliberar';

  @override
  String get empathizeFunction => 'Empatizar';

  @override
  String get altruisticValue => 'ALTRUÍSTA';

  @override
  String get empatheticSubtitle => 'Empático';

  @override
  String get predictFunction => 'Prever';

  @override
  String get analyzeFunction => 'Analisar';

  @override
  String get strategistValue => 'ESTRATEGISTA';

  @override
  String get analystSubtitle => 'Analista';

  @override
  String get trackFunction => 'Rastrear';

  @override
  String get simplifyFunction => 'Simplificar';

  @override
  String get tacticianValue => 'TÁTICO';

  @override
  String get synthesizerSubtitle => 'Sintetizador';

  @override
  String get selfEnjoyFunction => 'Auto-Desfrutar';

  @override
  String get selfMotivateFunction => 'Auto-Motivar';

  @override
  String get funnyValue => 'DIVERTIDO';

  @override
  String get enthusiasticSubtitle => 'Entusiástico';

  @override
  String get selfTransformFunction => 'Auto-Transformar';

  @override
  String get selfObserveFunction => 'Auto-Observar';

  @override
  String get versatileValue => 'VERSÁTIL';

  @override
  String get selfSeerSubtitle => 'Auto-Observador';

  @override
  String get executeFunction => 'Executar';

  @override
  String get discardFunction => 'Descartar';

  @override
  String get warriorValue => 'GUERREIRO';

  @override
  String get releaserSubtitle => 'Liberador';

  @override
  String get storeFunction => 'Armazenar';

  @override
  String get recognizeFunction => 'Reconhecer';

  @override
  String get guardianValue => 'GUARDIÃO';

  @override
  String get nurturerSubtitle => 'Cuidador';

  @override
  String get memoryHierarchyContent =>
      'Bower et al. (1969): A organização hierárquica pode melhorar a recordação em aproximadamente 200% comparado à apresentação aleatória.';

  @override
  String get baselineLabel => 'Linha Base';

  @override
  String get randomPresentationSublabel => 'capacidade de recordação';

  @override
  String get hierarchicalOrganizationLabel => 'Organização Hierárquica';

  @override
  String get hierarchicalImprovementSublabel => 'melhoria na recordação';

  @override
  String get brainProcessesHierarchically =>
      'Implicação provável: Seu cérebro provavelmente processa informações hierarquicamente por natureza. Lutar contra essa estrutura possivelmente desperdiça recursos cognitivos.';

  @override
  String get decisionFatigueContent =>
      'Baumeister et al. (1998): Após decisões não estruturadas repetidas, a qualidade das decisões provavelmente declina significativamente.';

  @override
  String get evolutionaryPerspectiveTitle => 'Perspectiva Evolutiva:';

  @override
  String get ancestorsDecisionsContent =>
      'Nossos ancestrais provavelmente enfrentavam decisões diárias limitadas em hierarquias sociais estruturadas. O caos moderno de opções desorganizadas pode exceder nossa capacidade cognitiva.';

  @override
  String get preOrganizedStructures =>
      'Estruturas hierárquicas pré-organizadas poderiam manter a qualidade das decisões mesmo sob carga cognitiva.';

  @override
  String get millerNumberContent =>
      'Miller (1956): Humanos podem possivelmente manter 7±2 itens não relacionados na memória de trabalho, mas provavelmente podem processar 7±2 categorias, cada uma contendo 7±2 subcategorias.';

  @override
  String get individualItemsLabel => 'Itens Individuais';

  @override
  String get workingMemoryLimitSublabel => 'limite da memória de trabalho';

  @override
  String get hierarchicalCapacityLabel => 'Capacidade Hierárquica';

  @override
  String get organizedElementsSublabel => 'elementos organizados';

  @override
  String get exponentialProcessingCapacity =>
      'Isso poderia criar capacidade de processamento exponencial através da hierarquia, liberando recursos mentais para reconhecimento de padrões e planejamento futuro.';

  @override
  String get ancestralMismatchContent =>
      'Humanos modernos possivelmente enfrentam aproximadamente 35.000 decisões diárias, enquanto nossos ancestrais provavelmente encontravam 70-100 decisões estruturadas em hierarquias sociais previsíveis.';

  @override
  String get ancestralDecisionsLabel => 'Decisões Ancestrais';

  @override
  String get structuredPerDaySublabel => 'estruturadas/dia';

  @override
  String get modernDecisionsLabel => 'Decisões Modernas';

  @override
  String get unstructuredPerDaySublabel => 'não estruturadas/dia';

  @override
  String get schwartzOptionsContent =>
      'Schwartz (2004): Mais de 8-10 opções não estruturadas podem diminuir a satisfação em 25% e a qualidade das decisões em 15%.';

  @override
  String get foragingEfficiencyContent =>
      'Stephens & Krebs (1986): Animais que organizaram o comportamento de busca hierarquicamente (território → manchas → recursos específicos) provavelmente mostraram 40-60% melhor eficiência energética.';

  @override
  String get energyEfficiencyLabel => 'Eficiência Energética';

  @override
  String get hierarchicalOrganizationSublabel => 'organização hierárquica';

  @override
  String get goalAchievementLabel => 'Conquista de Objetivos';

  @override
  String get structuredFrameworksSublabel => 'estruturas organizadas';

  @override
  String get gigerenzerFrameworksContent =>
      'Gigerenzer (2007): Pessoas usando estruturas de decisão hierárquicas possivelmente alcançam objetivos 35% mais rápido com 50% menos esforço.';

  @override
  String get compressionAdvantageContent =>
      'Shannon (1948): A organização hierárquica provavelmente alcança compressão de dados ótima. Aplicado a experiências de vida, isso poderia permitir processar exponencialmente mais informações.';

  @override
  String get applicationToMomentsTitle => 'Aplicação a Momentos:';

  @override
  String get compressionMomentsContent =>
      'Em vez de lembrar centenas de experiências desconectadas, a organização hierárquica possivelmente permite comprimir momentos similares em categorias, liberando recursos mentais para reconhecimento de padrões e planejamento futuro.';

  @override
  String get predictionMachineContent =>
      'Clark (2013): O cérebro possivelmente opera como uma \"máquina de predição\", constantemente gerando modelos de experiências futuras baseados em padrões passados.';

  @override
  String get neuralReductionLabel => 'Redução Neural';

  @override
  String get predictableExperiencesSublabel => 'experiências previsíveis';

  @override
  String get unpredictedActivityLabel => 'Atividade Imprevista';

  @override
  String get neuralActivitySublabel => 'atividade neural';

  @override
  String get organizedMomentTracking =>
      'O rastreamento organizado de momentos provavelmente cria melhores modelos preditivos, reduzindo a carga cognitiva e melhorando a precisão da tomada de decisões futuras.';

  @override
  String get entropyReductionContent =>
      'Bialek et al. (2001): Redes neurais usando processamento hierárquico possivelmente alcançam eficiência superior na transmissão de informações comparado a estruturas planas.';

  @override
  String get lifeApplicationEntropy =>
      'Aplicação à vida: A organização hierárquica de momentos provavelmente permite extrair máximo conhecimento das experiências enquanto minimiza o ruído cognitivo.';

  @override
  String get creativityMythCounterTitle => 'Contra-evidência:';

  @override
  String get creativityMythCounterContent =>
      '• Stokes (2005): Profissionais criativos com estruturas organizacionais possivelmente produzem trabalho mais inovador\n• Schwartz (2004): Muitas opções não estruturadas provavelmente diminuem a produção criativa\n• A organização hierárquica provavelmente reduz o ruído cognitivo, liberando recursos mentais para o pensamento criativo';

  @override
  String get successMythCounterContent =>
      '• Ericsson (2016): Executores de elite em todos os domínios provavelmente usam sistemas de prática e reflexão altamente estruturados\n• Indivíduos de alto desempenho provavelmente mostram habilidades organizacionais superiores, não menos estrutura\n• Sociedades caçadoras-coletoras bem-sucedidas provavelmente tinham sistemas de organização hierárquica complexos';

  @override
  String get hierarchyMythCounterContent =>
      '• Todas as sociedades de primatas bem-sucedidas provavelmente exibem organização hierárquica com papéis claros\n• O cérebro humano provavelmente evoluiu o processamento hierárquico como sua arquitetura fundamental\n• Mesmo sociedades igualitárias possivelmente mantêm organização hierárquica para diferentes domínios';

  @override
  String get simplicityMythCounterContent =>
      '• A complexidade apropriada provavelmente corresponde às demandas ambientais\n• A supersimplificação possivelmente leva ao fracasso do sistema\n• A complexidade bem estruturada provavelmente reduz a carga cognitiva\n• A organização hierárquica provavelmente alcança um equilíbrio ótimo entre simplicidade e riqueza de informações';

  @override
  String get anxietyMythCounterContent =>
      '• A incerteza não estruturada provavelmente gera mais ansiedade que a complexidade organizada\n• Estruturas claras possivelmente reduzem a ansiedade de decisão\n• A organização hierárquica provavelmente fornece estrutura preditiva que acalma o sistema nervoso\n• Estudos sugerem que a clareza organizacional reduz hormônios do estresse';

  @override
  String get fullPresentationMethodologyTitle => 'A Metodologia';

  @override
  String get fullPresentationMethodologyContent =>
      'Nossa abordagem combina insights psicológicos com análise prática:\n\n1. **Identificação de Momentos**: Reconheça momentos-chave em sua vida\n2. **Análise Dimensional**: Categorize através de quatro níveis\n3. **Foco Direcional**: Compreenda orientação interna vs externa\n4. **Avaliação de Impacto**: Avalie efeitos de motivação e satisfação\n5. **Reconhecimento de Padrões**: Descubra seus valores latentes';

  @override
  String get fullPresentationFourLevelsTitle =>
      'Quatro Níveis de Experiência Humana';

  @override
  String get fullPresentationFourLevelsContent =>
      'Estes níveis funcionam hierarquicamente, construindo uns sobre os outros para criar sua experiência completa.';

  @override
  String get fullPresentationDirectionalFocusTitle => 'Foco Direcional';

  @override
  String get fullPresentationDirectionalFocusContent =>
      'Todo momento tem componentes internos e externos, mas uma direção tipicamente domina. Compreender isso ajuda a revelar suas tendências e preferências naturais.';

  @override
  String get fullPresentationPracticalApplicationTitle => 'Aplicação Prática';

  @override
  String get fullPresentationPracticalApplicationContent =>
      'Use esta metodologia para:\n\n• **Rastrear padrões** em sua tomada de decisões\n• **Identificar gatilhos** para motivação e satisfação\n• **Compreender conflitos** entre diferentes aspectos de si mesmo\n• **Fazer melhores escolhas** alinhadas com seus valores\n• **Desenvolver estratégias** para crescimento pessoal';

  @override
  String get fullPresentationConclusionTitle => 'Sua Jornada Começa';

  @override
  String get fullPresentationConclusionContent =>
      'Agora que você compreende a metodologia, você pode:\n\n• Começar a registrar seus momentos-chave\n• Analisar seus padrões ao longo do tempo\n• Descobrir seus valores latentes únicos\n• Usar insights para desenvolvimento pessoal\n\nLembre-se: Autoconhecimento é o primeiro passo em direção à vida intencional.';

  @override
  String get fullPresentationElementalDescription =>
      'Respostas físicas e instintivas';

  @override
  String get fullPresentationPersonalDescription =>
      'Experiências e emoções individuais';

  @override
  String get fullPresentationInformationalDescription =>
      'Processos intelectuais e analíticos';

  @override
  String get fullPresentationSocialDescription =>
      'Conexões interpessoais e culturais';

  @override
  String get ancestralMismatchTitle => 'Desajuste do Ambiente Ancestral';

  @override
  String get socialHierarchyTitle => 'Hipótese de Hierarquia Social';

  @override
  String get foragingEfficiencyTitle => 'Modelo de Eficiência de Busca';

  @override
  String get compressionAdvantageTitle => 'A Vantagem da Compressão';

  @override
  String get predictionMachineTitle => 'O Cérebro como Máquina de Predição';

  @override
  String get entropyReductionTitle => 'Princípio de Redução de Entropia';

  @override
  String get creativityMythTitle =>
      'Mito: \'A organização mata a criatividade e a espontaneidade\'';

  @override
  String get successMythTitle =>
      'Mito: \'Pessoas bem-sucedidas não precisam de sistemas—apenas improvisam\'';

  @override
  String get hierarchyMythTitle =>
      'Mito: \'A hierarquia é antinatural e opressiva\'';

  @override
  String get simplicityMythTitle =>
      'Mito: \'O simples é sempre melhor que o complexo\'';

  @override
  String get anxietyMythTitle =>
      'Mito: \'A organização é só para pessoas ansiosas ou controladoras\'';

  @override
  String get socialHierarchyContent =>
      'Sapolsky (2017): Humanos e outros primatas provavelmente mostram os níveis mais baixos de hormônios do estresse quando sua posição social está claramente definida, é previsível e controlada internamente.';

  @override
  String get stressReductionElementsTitle =>
      'Elementos-Chave para Redução do Estresse:';

  @override
  String get stressReductionElementsList =>
      '• Clareza hierárquica: Posição definida\n• Previsibilidade: Regras consistentes\n• Controle interno: Agência dentro da estrutura';

  @override
  String get randomPresentationLabel => 'Apresentação Aleatória';

  @override
  String get hierarchicalOrganizationBenefits =>
      'A organização hierárquica da vida pode imitar estruturas sociais ancestrais bem-sucedidas, provavelmente reduzindo o estresse e melhorando a tomada de decisões.';

  @override
  String get fourLevelsProcessTitle => 'Processo dos Quatro Níveis';

  @override
  String get hierarchicalStructureTitle => 'Estrutura Hierárquica';

  @override
  String get hierarchicalStructureDescription =>
      'As quatro dimensões seguem uma estrutura hierárquica:';

  @override
  String get levelsEquationText =>
      'Elemental + Pessoal + Informacional + Social = 100%';

  @override
  String get elementalAllocationText =>
      'Quando você aloca uma porcentagem ao Elemental, a porcentagem restante é distribuída entre Pessoal, Informacional e Social.';

  @override
  String get personalAllocationText =>
      'Quando você aloca uma porcentagem ao Pessoal, a porcentagem restante é distribuída entre Informacional e Social.';

  @override
  String get informationalAllocationText =>
      'Quando você aloca uma porcentagem ao Informacional, a porcentagem restante vai para o Social.';

  @override
  String get directionsExplanationText =>
      'Interior + Exterior = 100%\n\nEssas dimensões representam sua orientação ou abordagem para cada momento.';

  @override
  String get pleaseEnterTitle => 'Por favor, insira um título';

  @override
  String get titleMustBeTwoWords =>
      'O título deve ter exatamente duas palavras';

  @override
  String get untitledMoment => 'Momento Sem Título';

  @override
  String get descriptionNotAvailable => 'Descrição não disponível.';

  @override
  String get functionCollaborate => 'Colaborar';

  @override
  String get functionNegotiate => 'Negociar';

  @override
  String get functionDeliberate => 'Deliberar';

  @override
  String get functionEmpathize => 'Empatizar';

  @override
  String get functionPredict => 'Prever';

  @override
  String get functionAnalyze => 'Analisar';

  @override
  String get functionTrack => 'Rastrear';

  @override
  String get functionSimplify => 'Simplificar';

  @override
  String get functionSelfEnjoy => 'Auto-Desfrutar';

  @override
  String get functionSelfMotivate => 'Auto-Motivar';

  @override
  String get functionSelfTransform => 'Auto-Transformar';

  @override
  String get functionSelfObserve => 'Auto-Observar';

  @override
  String get functionExecute => 'Executar';

  @override
  String get functionDiscard => 'Descartar';

  @override
  String get functionStore => 'Armazenar';

  @override
  String get functionRecognize => 'Reconhecer';

  @override
  String get valueCollaborator => 'COLABORADOR';

  @override
  String get valueAltruistic => 'ALTRUÍSTA';

  @override
  String get valueStrategist => 'ESTRATEGISTA';

  @override
  String get valueTactician => 'TÁTICO';

  @override
  String get valueFunny => 'DIVERTIDO';

  @override
  String get valueVersatile => 'VERSÁTIL';

  @override
  String get valueWarrior => 'GUERREIRO';

  @override
  String get valueGuardian => 'GUARDIÃO';

  @override
  String get subtitleDiplomat => 'Diplomata';

  @override
  String get subtitleEmpathetic => 'Empático';

  @override
  String get subtitleAnalyst => 'Analista';

  @override
  String get subtitleSynthesizer => 'Sintetizador';

  @override
  String get subtitleEnthusiastic => 'Entusiástico';

  @override
  String get subtitleSelfSeer => 'Auto-Observador';

  @override
  String get subtitleReleaser => 'Liberador';

  @override
  String get subtitleNurturer => 'Cuidador';

  @override
  String get descriptionGuardian =>
      'Valor focado em preservar, proteger e manter estabilidade. Representa a capacidade de reconhecer e armazenar informações importantes para segurança e continuidade.';

  @override
  String get descriptionWarrior =>
      'Valor focado em ação, execução e superação de obstáculos. Representa a capacidade de executar decisões e descartar o que não serve mais.';

  @override
  String get descriptionVersatile =>
      'Valor focado em adaptabilidade e auto-observação. Representa a capacidade de se transformar e observar a si mesmo para crescimento pessoal.';

  @override
  String get descriptionFunny =>
      'Valor focado em prazer, motivação e energia positiva. Representa a capacidade de auto-desfrutar e auto-motivar para manter o bem-estar.';

  @override
  String get descriptionStrategist =>
      'Valor focado em planejamento e análise profunda. Representa a capacidade de prever cenários e analisar informações complexas.';

  @override
  String get descriptionTactician =>
      'Valor focado em síntese e rastreamento eficiente. Representa a capacidade de simplificar complexidades e rastrear progresso.';

  @override
  String get descriptionAltruistic =>
      'Valor focado em empatia e consideração pelos outros. Representa a capacidade de deliberar e empatizar para o bem comum.';

  @override
  String get descriptionCollaborator =>
      'Valor focado em cooperação e negociação. Representa a capacidade de colaborar efetivamente e negociar soluções mutuamente benéficas.';
}
