{"@@locale": "en", "appTitle": "Estimat KeyMoments", "@appTitle": {"description": "The title of the application"}, "selectLanguageTitle": "Select Language", "@selectLanguageTitle": {"description": "Title for language selection screen"}, "englishLanguage": "English", "portugueseLanguage": "Portuguese", "spanishLanguage": "Spanish", "onboardingScreenTitle": "Welcome to Estimat KeyMoments", "@onboardingScreenTitle": {"description": "Title for the onboarding screen"}, "onboardingScene1Message": "Noise can be simplified into notes and turned into music", "onboardingScene1MessagePart2": "With a bit of proportion...", "onboardingScene2Message": "Any image can be replicated through light, shadow, and color, and transformed into art", "onboardingScene2MessagePart2": "With a bit of proportion...", "onboardingScene3Message": "Your cognitive overload—could you refine what you value most in yourself?", "onboardingScene3MessagePart2": "With a bit of ...", "onboardingScene4Message": "With information theory, an AI can reduce complex data to 5% and reconstruct it with 95% functional fidelity; these are latent vectors", "onboardingScene4MessagePart2": "Maybe you could apply a bit of that to your latent values...", "onboardingFlowchartMessage": "Let's identify our reference moments. Track the flows associated with those moments. Evaluate according to their function. Discover the latent values that drive us:", "onboardingFlowchartTitle": "Process Flow", "nextButton": "Next", "previousButton": "Previous", "getStartedButton": "Get Started", "exitToAppButton": "Exit to App", "@exitToAppButton": {"description": "Button text to exit the onboarding flow and go to the main app"}, "motivationAppBarTitle": "Motivation Moments", "satisfactionAppBarTitle": "Satisfaction Moments", "selectMomentTypeTitle": "Select Moment Type", "selectImprovementWorsening": "Choose between Improvement or Worsening:", "selectMotivationSatisfaction": "Choose between Motivation or Satisfaction:", "improvesLabel": "Improves", "worsensLabel": "Worsens", "motivationLabel": "Motivation", "satisfactionLabel": "Satisfaction", "elementalLabel": "Elemental", "@elementalLabel": {"description": "Label for elemental level"}, "personalLabel": "Personal", "@personalLabel": {"description": "Label for personal level"}, "informationalLabel": "Informational", "@informationalLabel": {"description": "Label for informational level"}, "socialLabel": "Social", "@socialLabel": {"description": "Label for social level"}, "elementalExplanation": "Based on environmental stimuli, we give a response that was modulated by evolution. More correlated with physical activities: Recognition, consumption, rest, storage, execute, fight, attack, and struggle.", "@elementalExplanation": {"description": "Explanation of the elemental level"}, "personalExplanation": "Adding to the memory that comes from our ancestors, we have a memory that is created from the unique interaction between an individual and their environment. ainful and pleasant. More correlated with: Error observation, adaptation, pain awareness, positive focus, pleasure, success enhancement", "@personalExplanation": {"description": "Explanation of the personal level"}, "informationalExplanation": "Upon reaching the intellectual level, in addition to integrating memories and experiences with the environment, we achieve a unique ability: to relate memories or an individual's own memories to each other and encode abstract information (example: Trend analysis, hypothesis creation, prediction, Generalization, efficiency, tracking).", "@informationalExplanation": {"description": "Explanation of the informational level"}, "socialExplanation": "Social relationships involve the connection of information between individuals, the exchange of culture and communication. We can define a boundary between intellectual level and social level when we use our intellectual processes to interact with other beings capable of combining their own memories. To Empathy, consideration of others' needs, Communication, cooperation, shared goals", "@socialExplanation": {"description": "Explanation of the social level"}, "inwardLabel": "Inward", "@inwardLabel": {"description": "Label for inward direction"}, "outwardLabel": "Outward", "@outwardLabel": {"description": "Label for outward direction"}, "inwardExplanation": "Reflexive: To store, self-transform, predict, empathize.", "@inwardExplanation": {"description": "Explanation of inward direction"}, "outwardExplanation": "Intuitive: To execute, enjoy, track, collaborate.", "@outwardExplanation": {"description": "Explanation of outward direction"}, "titleInputHint": "Enter a two-word title", "descriptionInputHint": "Describe this moment", "evidenceInputHint": "Provide evidence for this distribution", "describeDistributionHint": "Describe why you chose this distribution...", "firstMomentEvidenceHint": "Explain why this moment creates more possibilities than just being alive. What new opportunities, paths, or potential does it open up?", "comparisonEvidenceHint": "Explain how many possibilities this moment creates compared to your life baseline...", "lifePossibilitiesFactorTitle": "Life Possibilities Factor", "howManyPossibilitiesTitle": "How Many Possibilities Does This Moment Create", "comparedToLifeBaseline": "Compared to your life baseline, this moment creates:", "morePossibilitiesButton": "More Possibilities", "fewerPossibilitiesButton": "Fewer Possibilities", "vsLifeBaseline": "vs Life Baseline", "explainWhyFirstMomentTitle": "Explain Why This Moment Creates More Possibilities Than Life", "provideEvidenceTitle": "Provide Evidence for this Possibilities Assessment", "continueButton": "Continue", "lifePossibilitiesChart": "Life Possibilities Chart", "allTimeFilter": "All Time", "lastWeekFilter": "Last Week", "lastMonthFilter": "Last Month", "last3MonthsFilter": "Last 3 Months", "currentPreviewLabel": "Current Preview:", "lifeLabel": "Life", "previewLabel": "Preview", "lifeBaselineLabel": "Life Baseline (1x)", "morePossibilitiesLabel": "More Possibilities", "fewerPossibilitiesLabel": "Fewer Possibilities", "currentPreviewLegend": "Current Preview", "lifePossibilitiesExplanation": "Compare this moment to your life baseline (1x). How many times more possibilities does this moment create for you? You can go above or below previous moments, but never below the life baseline.\\n\\nThe factorial calculation represents the exponential growth of possibilities that meaningful moments can create in your life.", "minimumLifeBaselineNote": "Minimum: 1.0x (life baseline)", "guardianLabel": "Guardian", "warriorLabel": "Warrior", "versatileLabel": "Versatile", "funLabel": "Fun", "strategistLabel": "Strategist", "tacticalLabel": "Tactical", "altruistLabel": "Altruist", "collaboratorLabel": "Collaborator", "summaryTitle": "Summary", "motivationSectionTitle": "Motivation Analysis", "satisfactionSectionTitle": "Satisfaction Analysis", "latentValuesSectionTitle": "Latent Values", "improvesMotivationLabel": "Improves Motivation", "worsensMotivationLabel": "Worsens Motivation", "improvesSatisfactionLabel": "Improves Satisfaction", "worsensSatisfactionLabel": "Worsens Satisfaction", "proportionLabel": "Proportion", "exportDataButton": "Export Data", "viewOnboardingMenu": "Onboarding", "viewFullPresentationMenu": "Full Presentation", "viewLevelProcessFocusMenu": "Levels and Directions", "viewLatentValuesMenu": "Evolutive Functions and Values", "fromIACodeToHumanValuesMenu": "From IA code to human values", "jacoMinestSupportMenu": "Jacominest Support", "changeLanguageMenu": "Change Language", "saveButton": "Save", "cancelButton": "Cancel", "backButton": "Back", "whyMomentsScreenTitle": "Why Moments?", "whyMomentsMenuTitle": "Why Moments?", "whyHierarchicalMenuTitle": "Why Hierarchical Organization?", "@whyHierarchicalMenuTitle": {"description": "Menu title for Why Hierarchical Organization screen"}, "whyHierarchicalScreenTitle": "Why Hierarchical Organization", "levelsAndDirectionsScreenTitle": "Levels and Directions", "skipForNowButton": "Skip for now", "noMomentsRecordedYet": "No moments recorded yet.", "pageCounter": "{current} / {total}", "@pageCounter": {"description": "Page counter showing current page out of total pages", "placeholders": {"current": {"type": "int"}, "total": {"type": "int"}}}, "welcomeToEstimatKeyMoments": "Welcome to Estimat KeyMoments", "estimatKeyMomentsDescription": "A comprehensive methodology for understanding and analyzing the key moments that shape your life decisions and personal growth.", "editThisMomentButton": "Edit this moment", "closeButton": "Close", "insertNewMomentButton": "Insert a new moment", "viewAllMomentsButton": "View All Moments", "momentsHistoryTitle": "Moments History", "momentSavedTitle": "{momentType} Saved", "@momentSavedTitle": {"description": "Title for moment saved dialog", "placeholders": {"momentType": {"type": "String"}}}, "momentSavedMessage": "Your moment has been saved successfully.", "factorialComparisonLabel": "Factorial Comparison: {comparisonType}", "@factorialComparisonLabel": {"description": "Label for factorial comparison", "placeholders": {"comparisonType": {"type": "String"}}}, "improvementLabel": "Improvement", "worseningLabel": "Worsening", "factorialSliderValueLabel": "Factorial Slider Value: {value}", "@factorialSliderValueLabel": {"description": "Label for factorial slider value", "placeholders": {"value": {"type": "String"}}}, "factorialMultiplierLabel": "Factorial Multiplier: {value}", "@factorialMultiplierLabel": {"description": "Label for factorial multiplier", "placeholders": {"value": {"type": "String"}}}, "whyRegisterMomentsTitle": "Why does it matter to record moments more objectively?", "whyRegisterMomentsContent": "Your emotional peaks and valleys aren't just scattered data: they're like the coordinates of your internal compass. Recording your motivation (how driven you feel) and your satisfaction (how fulfilled you feel) gives you a clear map to plan goals, routines or tasks that really add to your life. This becomes key when you discover where these two things misalign, because there you can adjust and design activities that work better for you.", "whenNoticeMoreTitle": "When is this most noticeable?", "whenNoticeMoreContent": "When you're in an emotional low. In those moments, your brain tricks you with thoughts like: \"I never get anything right.\" \"There's nothing that motivates me.\" Although this isn't true, memory bias—that tendency to remember the negative or most recent—makes you feel that way. 📈 Keeping an objective record of your high moments gives you real proof of who you are when you're well, so you don't get lost in the fog when you're down.", "highMotivationSatisfactionTitle": "High motivation and high satisfaction", "highMotivationSatisfactionContent": "Quick question: What was the most significant thing that happened to you this month? If you had a daily record of your emotions, would you answer the same? Probably not. And there's a reason: Memory bias: According to <PERSON><PERSON> and <PERSON><PERSON><PERSON> (1979), our brain tends to give more weight to what's recent or intense (this is called the peak-end rule). That's why we sometimes forget valuable moments that weren't so \"noisy\". Solution: Writing down your peaks compensates for this mental trap and shows you the complete picture.", "evolutionaryViewTitle": "Evolutionary perspective", "evolutionaryViewContent": "Strong positive emotions—like pride or fulfillment—are signals of opportunities: a relationship that works, a personal achievement or a decision that aligns you with what you want. Our ancestors survived better if they remembered these moments to repeat them. Your brain isn't designed just to make you happy, but to help you thrive. If you know what motivates you, you can seek it more often.", "practiceEstimatTitle": "Practice with ESTIMAT:", "practiceEstimatContent": "Imagine you write down: ✍️ \"I felt powerful explaining my emotions in a workshop without being judged.\" 🔁 When reviewing several similar entries, ESTIMAT shows you: High motivation when you express what you feel. High satisfaction with active listening and validation. 🧭 What do you do with this? Look for or create more situations like this (workshops, talks with understanding friends). Use it as an anchor when you feel lost.", "highMotivationLowSatisfactionTitle": "High Motivation + Low Satisfaction", "highMotivationLowSatisfactionContent": "Have you ever been really excited about something and then felt \"meh\"? Example: You dream about the new iPhone 📱, rush to buy it and the next day think: \"Was this it?\" Impact bias: According to <PERSON> and <PERSON>, we tend to overestimate future happiness by 40-60%. It's as if your brain puts an Instagram filter on what you expect, so you move towards it. Reality: When the moment arrives, it's not always up to the hype.", "lowMotivationHighSatisfactionTitle": "Low Motivation + High Satisfaction", "lowMotivationHighSatisfactionContent": "Have you ever dragged yourself to do something and ended up surprised by how good you felt? ⛈️😊 Example: You don't want to go to the gym, but you leave with a smile. Underestimation: Studies from the University of British Columbia show that we underestimate the pleasure of exercise by 20-30%. You think it will be +1, but it ends up being +3. Effort paradox: The effort itself gives you a satisfaction bonus—every step counts.", "lowMotivationLowSatisfactionTitle": "Low Motivation + Low Satisfaction", "lowMotivationLowSatisfactionContent": "What about those days when there's no desire or pleasure? 😔 You could ignore them or force yourself to continue, but what if those lows were like seeds for something bigger? Reflecting on those difficult moments not only helps you get out of the pit, but can teach you how to avoid falling so deep next time and even ignite new ideas. A 2020 study in Frontiers in Psychology explored how thinking deeply about problems can help when you're down. They found that people who reflected on their problems, seeking solutions, felt less depressed after a few weeks.", "evolutiveFunctionsHeader": "Evolutive Functions", "@evolutiveFunctionsHeader": {"description": "Header for evolutive functions column in latent values screen"}, "valueDescriptionGuardian": "Recognize: You recognize, consume, ingest.\nStore: You rest, store, and metabolize.", "@valueDescriptionGuardian": {"description": "Description for Guardian latent value"}, "valueDescriptionWarrior": "Discard: You discard, flee, inhibit.\nExecute: Fight, attack, and struggle.", "@valueDescriptionWarrior": {"description": "Description for Warrior latent value"}, "valueDescriptionVersatile": "Self-Observe: You highlight negative information, feel pain, observe errors.\nSelf-Transform: You reduce your errors and adapt stimuli.", "@valueDescriptionVersatile": {"description": "Description for Versatile latent value"}, "valueDescriptionFunny": "Self-Motivate: You highlight positive information, feel pleasure.\nSelf-Enjoy: I enhance successes and contrast attitudes and ideas.", "@valueDescriptionFunny": {"description": "Description for Funny latent value"}, "valueDescriptionStrategist": "Analyze: You review trends, ask if something could be false, analyze.\nPredict: You predict what is most probable and create hypotheses.", "@valueDescriptionStrategist": {"description": "Description for Strategist latent value"}, "valueDescriptionTactician": "Simplify: You generalize, compare the easiest, fastest way.\nTrack: You search, hunt, and trace.", "@valueDescriptionTactician": {"description": "Description for Tactician latent value"}, "valueDescriptionAltruistic": "Empathize: You empathize with what is important for others.\nDeliberate: You consider myself and the needs of as many people as possible, practicing efficiency altruism.", "@valueDescriptionAltruistic": {"description": "Description for Altruistic latent value"}, "valueDescriptionCollaborator": "Negotiate: You help to understand, communicate.\nCollaborate: You cooperate toward shared goals.", "@valueDescriptionCollaborator": {"description": "Description for Collaborator latent value"}, "latentValuesTitle": "Latent Values", "@latentValuesTitle": {"description": "Title for latent values section"}, "lifePossibilitiesChartTitle": "Life Possibilities Chart", "@lifePossibilitiesChartTitle": {"description": "Title for life possibilities chart section"}, "periodLabel": "Period:", "@periodLabel": {"description": "Label for period selection"}, "last7DaysFilter": "Last 7 Days", "@last7DaysFilter": {"description": "Filter option for last 7 days"}, "customRangeFilter": "Custom Range", "@customRangeFilter": {"description": "Filter option for custom date range"}, "customDateRangeFilterTitle": "Custom Date Range Filter", "@customDateRangeFilterTitle": {"description": "Title for custom date range filter section"}, "startDateLabel": "Start Date:", "@startDateLabel": {"description": "Label for start date selection"}, "endDateLabel": "End Date:", "@endDateLabel": {"description": "Label for end date selection"}, "selectStartDateHint": "Select start date", "@selectStartDateHint": {"description": "Hint text for start date selection"}, "selectEndDateHint": "Select end date", "@selectEndDateHint": {"description": "Hint text for end date selection"}, "resetButton": "Reset", "@resetButton": {"description": "Reset button text"}, "customRangeActiveLabel": "Custom range active", "@customRangeActiveLabel": {"description": "Label indicating custom range is active"}, "showingMomentsLabel": "Showing {count} moment{plural}", "@showingMomentsLabel": {"description": "Label showing number of moments displayed", "placeholders": {"count": {"type": "int"}, "plural": {"type": "String"}}}, "whyFocusOnMomentsTitle": "Why Focus on Moments?", "@whyFocusOnMomentsTitle": {"description": "Title for why focus on moments section"}, "whyFocusOnMomentsContent": "Key moments are the building blocks of our decision-making process. By understanding these moments, we can:\n\n• Identify patterns in our behavior\n• Understand what truly motivates us\n• Recognize what brings us satisfaction\n• Make more conscious choices\n• Develop better self-awareness", "@whyFocusOnMomentsContent": {"description": "Content explaining why focus on moments"}, "discoveringLatentValuesTitle": "Discovering Latent Values", "@discoveringLatentValuesTitle": {"description": "Title for discovering latent values section"}, "discoveringLatentValuesContent": "Your latent values emerge from the combination of:\n\n• **Level percentages** (how you distribute across the four levels)\n• **Directional focus** (inward vs outward orientation)\n• **Impact type** (improves vs worsens)\n\nThese values reveal your core strengths: {guardianLabel}, {warriorLabel}, {versatileLabel}, {funLabel}, and others.", "@discoveringLatentValuesContent": {"description": "Content explaining discovering latent values", "placeholders": {"guardianLabel": {"type": "String"}, "warriorLabel": {"type": "String"}, "versatileLabel": {"type": "String"}, "funLabel": {"type": "String"}}}, "understandingLatentValuesTitle": "Understanding Latent Values", "@understandingLatentValuesTitle": {"description": "Title for understanding latent values section"}, "understandingLatentValuesContent": "Your latent values emerge from how you distribute your focus across the four hierarchical levels and whether you tend toward inward or outward orientation. Each combination reveals core strengths and natural tendencies that guide your decision-making and life satisfaction.", "@understandingLatentValuesContent": {"description": "Content explaining understanding latent values"}, "directionsFocusTitle": "Directions Focus", "@directionsFocusTitle": {"description": "Title for directions focus section"}, "whyHierarchicalOrganizationTitle": "Why Hierarchical Organization of Life Moments Might Matter", "@whyHierarchicalOrganizationTitle": {"description": "Title for why hierarchical organization section"}, "dataVisualizationBiasReductionTitle": "Data Visualization & Bias Reduction", "@dataVisualizationBiasReductionTitle": {"description": "Title for data visualization and bias reduction section"}, "experimentalEvidenceTitle": "Experimental Evidence", "@experimentalEvidenceTitle": {"description": "Title for experimental evidence section"}, "evolutionaryPsychologyPerspectiveTitle": "Evolutionary Psychology Perspective", "@evolutionaryPsychologyPerspectiveTitle": {"description": "Title for evolutionary psychology perspective section"}, "recognizeYourMomentOrientationLevel": "Recognize your moment, your orientation and level", "recognizeYourMoment": "Recognize your moment", "organizeTheMomentAndLookForEvidence": "Now let's look at the distribution of information at the moment and look for evidence.", "orientationTitle": "Orientation", "orientationQuestion": "Was your moment more focused on changing internally or externally?", "threeLevelsTitle": "Levels", "@threeLevelsTitle": {"description": "Title for the 3 levels section that encompasses all level comparisons"}, "threeLevelsDescription": "Hierarchical analysis of your moment across the four levels of human experience", "@threeLevelsDescription": {"description": "Description of the 3 levels concept"}, "pieStepElementalPersonalTitle": "3 Levels: Elemental vs Personal", "pieStepElementalPersonalDescription": "For your moment you focused more on your body state or on your personal interests/emotions?", "pieStepPersonalInformationalTitle": "3 Levels: Personal vs Informational", "pieStepPersonalInformationalDescription": "For your moment you focused more on your personal interests/emotions or on your gathering and processing information?", "pieStepInformationalSocialTitle": "3 Levels: Informational vs Social", "pieStepInformationalSocialDescription": "For your moment you focused more on analyzing information or on connecting with others?", "pieStepEvidenceQuestion": "What evidence or variables influenced your choice?", "whyMomentsHeaderSubtitle": "Analyze your moments, know your patterns", "whyRegisterMomentsObjectivelyTitle": "Why register moments objectively?", "whyRegisterMomentsObjectivelyContent": "Your emotional peaks and valleys are not just loose data: they are like the coordinates of your internal compass. Recording your motivation (how driven you feel) and your satisfaction (how fulfilled you feel) gives you a clear map to plan goals, routines or tasks that really add to your life.", "whyRegisterMomentsObjectivelyHighlight": "📈 Keeping a record of your motivation and satisfaction peaks serves to compensate for emotional distortion. It allows you to have evidence of who you are when you're well, so you don't lose sight of yourself when you're not.", "highMotivationHighSatisfactionTitle": "High Motivation + High Satisfaction", "researchInfoBoxTitle": "🧠 Research", "researchInfoBoxContent": "According to <PERSON><PERSON> and <PERSON><PERSON><PERSON> (1979), our brain tends to give more weight to what is recent or intense (peak-end rule). That's why we sometimes forget valuable moments that weren't so \"noisy\".", "evolutionaryViewInfoBoxTitle": "🔬 Evolutionary View", "evolutionaryViewInfoBoxContent": "Strong positive emotions signal adaptive opportunities: successful relationships, purpose-aligned decisions, social or personal achievements.", "practiceEstimatInfoBoxTitle": "✍️ Practice with ESTIMAT", "practiceEstimatInfoBoxContent": "By reviewing multiple entries, ESTIMAT shows you patterns and helps you replicate those moments of high motivation and satisfaction.", "highMotivationLowSatisfactionIntro": "Have you ever been really excited about something and then felt a \"meh\"? 📱", "impactBiasInfoBoxTitle": "📊 Impact Bias", "impactBiasInfoBoxContent": "According to <PERSON> and <PERSON>, we tend to overestimate future happiness by", "practiceInfoBoxTitle": "🎯 Practice", "practiceInfoBoxContent": "• Simulate: Before diving in, imagine colors, smells, sounds\n• Prediction vs. reality: Write down how much you think you'll enjoy it\n• Adjust: If you expected +3 and it was +1, rethink if it's worth repeating", "lowMotivationHighSatisfactionIntro": "Have you ever dragged yourself to do something and ended up surprised by how good you felt? ⛈️😊", "pleasureUnderestimationInfoBoxTitle": "🏃‍♂️ Pleasure Underestimation", "pleasureUnderestimationInfoBoxContent": "The University of British Columbia showed that people underestimate their enjoyment of exercise by", "effortParadoxInfoBoxTitle": "🧬 Effort Paradox", "effortParadoxInfoBoxContent": "Every drop of effort is exchanged for an extra plus of satisfaction. That \"burst of joy\" is your brain saying \"Well done!\"", "lowMotivationLowSatisfactionIntro": "And those days when there's no desire or pleasure? 😔 Those lows might be seedbeds for your best ideas.", "reflectionPowerInfoBoxTitle": "📈 Power of Reflection", "reflectionPowerInfoBoxContent": "Students who practiced reflective self-evaluation showed an increase of", "practiceEstimatLowInfoBoxTitle": "🎯 Practice with ESTIMAT", "practiceEstimatLowInfoBoxContent": "• Record your low: Write without filters how you feel\n• Review your log: ESTIMAT will show you patterns\n• Do something small: A walk, a song, three gratitudes", "generalOverviewTitle": "The General Overview", "generalOverviewIntro": "Recording your emotions is not just a hobby—it's a tool to know yourself and get the most out of your brain.", "memoryBiasStatTitle": "🧠 Memory bias", "memoryBiasStatContent": "We overvalue what's recent or intense", "impactBiasStatTitle": "🎯 Impact bias", "impactBiasStatContent": "We overestimate future happiness", "underestimationStatTitle": "💪 Underestimation", "underestimationStatContent": "We enjoy exercise more than we think", "recoveryStatTitle": "🔄 Recovery", "recoveryStatContent": "Reflecting gives you more motivated days", "generalOverviewConclusion": "What small step can you take today to better understand your emotions? Try writing down a peak and a valley—the results might surprise you. ✨", "whyHierarchicalHeaderSubtitle": "Why Hierarchical Organization of Life Moments Might Matter", "whyHierarchicalImportantNote": "Important: If you're already experiencing consistent well-being and deep satisfaction, additional organization is likely unnecessary. This approach seems most relevant during transitions, complex decisions, or persistent dissatisfaction.", "informationTheoryPerspectiveTitle": "Information Theory Perspective", "debunkingCommonMythsTitle": "Debunking Common Myths", "selfPerceptionBiasesTitle": "The Problem of Self-Perception Biases", "visualProportionsTitle": "Advantages of Visual Proportions", "statsVsIntuitionTitle": "Personal Statistics vs. Intuition", "memoryHierarchyTitle": "Memory Hierarchy Experiments", "decisionFatigueTitle": "Decision Fatigue Studies", "millerNumberTitle": "<PERSON>'s Magical Number", "availabilityBiasContent": "Availability Bias (<PERSON><PERSON><PERSON> & <PERSON>eman, 1973): We're likely to disproportionately remember recent or emotional events, which could distort our perception of life patterns.", "overestimationLabel": "Overestimation", "overestimationSublabel": "of recent vs. actual patterns", "decisionDistortionLabel": "Decision Distortion", "decisionDistortionSublabel": "from memory-based choices", "hierarchicalVisualizationNote": "Hierarchical moment visualization might counteract this bias by providing a more objective representation of temporal patterns.", "clevelandMcGillContent": "Cleveland & McGill (1984): Visual perception of proportions appears to be significantly more accurate than narrative memories for evaluating temporal distributions.", "potentialPersonalApplicationsTitle": "Potential Personal Applications:", "personalApplicationsList": "• Actual time distribution vs. perception\n• Energy patterns and emotional states\n• Frequency of different experience types\n• Progress toward long-term objectives", "visualizationDiscrepanciesNote": "These visualizations could reveal discrepancies between subjective perception and objective reality, facilitating more informed decisions.", "personalIntuitionParadoxContent": "Personal Intuition Paradox: While we trust our intuition for personal decisions, we're likely to apply rigorous statistical analysis for professional or financial decisions.", "financialDecisionsLabel": "Financial Decisions", "financialDecisionsSublabel": "use objective data", "personalDecisionsLabel": "Personal Decisions", "personalDecisionsSublabel": "use objective data", "potentialImprovementLabel": "Potential Improvement", "potentialImprovementSublabel": "with systematic organization", "hierarchicalAnalyticalNote": "Hierarchical organization might allow applying analytical rigor to life decisions while maintaining emotional and intuitive flexibility.", "socialLevelTitle": "4. Social", "informationalLevelTitle": "3. Informational", "personalLevelTitle": "2. Personal", "elementalLevelTitle": "1. Elemental", "collaborateFunction": "Collaborate", "negotiateFunction": "Negotiate", "collaboratorValue": "COLLABORATOR", "diplomatSubtitle": "Diplomat", "deliberateFunction": "Deliberate", "empathizeFunction": "Empathize", "altruisticValue": "ALTRUISTIC", "empatheticSubtitle": "Empathetic", "predictFunction": "Predict", "analyzeFunction": "Analyze", "strategistValue": "STRATEGIST", "analystSubtitle": "Analyst", "trackFunction": "Track", "simplifyFunction": "Simplify", "tacticianValue": "TACTICIAN", "synthesizerSubtitle": "Synthesizer", "selfEnjoyFunction": "Self-Enjoy", "selfMotivateFunction": "Self-Motivate", "funnyValue": "FUNNY", "enthusiasticSubtitle": "Enthusiastic", "selfTransformFunction": "Self-Transform", "selfObserveFunction": "Self-Observe", "versatileValue": "VERSATILE", "selfSeerSubtitle": "SelfSeer", "executeFunction": "Execute", "discardFunction": "Discard", "warriorValue": "WARRIOR", "releaserSubtitle": "Releaser", "storeFunction": "Store", "recognizeFunction": "Recognize", "guardianValue": "GUARDIAN", "nurturerSubtitle": "Nurturer", "memoryHierarchyContent": "<PERSON><PERSON> et al. (1969): Hierarchical organization can improve recall by approximately 200% compared to random presentation.", "baselineLabel": "Baseline", "randomPresentationSublabel": "recall capacity", "hierarchicalOrganizationLabel": "Hierarchical Organization", "hierarchicalImprovementSublabel": "improvement in recall", "brainProcessesHierarchically": "Likely implication: Your brain probably processes information hierarchically by nature. Fighting against this structure possibly wastes cognitive resources.", "decisionFatigueContent": "<PERSON><PERSON><PERSON> et al. (1998): After repeated unstructured decisions, decision quality probably declines significantly.", "evolutionaryPerspectiveTitle": "Evolutionary Perspective:", "ancestorsDecisionsContent": "Our ancestors probably faced limited daily decisions in structured social hierarchies. Modern chaos of disorganized choices may exceed our cognitive capacity.", "preOrganizedStructures": "Pre-organized hierarchical structures could maintain decision quality even under cognitive load.", "millerNumberContent": "<PERSON> (1956): Humans can possibly maintain 7±2 unrelated items in working memory, but probably can process 7±2 categories, each containing 7±2 subcategories.", "individualItemsLabel": "Individual Items", "workingMemoryLimitSublabel": "working memory limit", "hierarchicalCapacityLabel": "Hierarchical Capacity", "organizedElementsSublabel": "organized elements", "exponentialProcessingCapacity": "This could create exponential processing capacity through hierarchy, freeing mental resources for pattern recognition and future planning.", "ancestralMismatchContent": "Modern humans possibly face approximately 35,000 daily decisions, while our ancestors probably encountered 70-100 structured decisions in predictable social hierarchies.", "ancestralDecisionsLabel": "Ancestral Decisions", "structuredPerDaySublabel": "structured/day", "modernDecisionsLabel": "Modern Decisions", "unstructuredPerDaySublabel": "unstructured/day", "schwartzOptionsContent": "<PERSON> (2004): More than 8-10 unstructured options can decrease satisfaction by 25% and decision quality by 15%.", "foragingEfficiencyContent": "<PERSON> (1986): Animals that organized foraging behavior hierarchically (territory → patches → specific resources) likely showed 40-60% better energy efficiency.", "energyEfficiencyLabel": "Energy Efficiency", "hierarchicalOrganizationSublabel": "hierarchical organization", "goalAchievementLabel": "Goal Achievement", "structuredFrameworksSublabel": "structured frameworks", "gigerenzerFrameworksContent": "<PERSON><PERSON><PERSON><PERSON> (2007): People using hierarchical decision frameworks possibly achieve goals 35% faster with 50% less effort.", "compressionAdvantageContent": "<PERSON> (1948): Hierarchical organization probably achieves optimal data compression. Applied to life experiences, this could allow processing exponentially more information.", "applicationToMomentsTitle": "Application to Moments:", "compressionMomentsContent": "Instead of remembering hundreds of disconnected experiences, hierarchical organization possibly allows compressing similar moments into categories, freeing mental resources for pattern recognition and future planning.", "predictionMachineContent": "<PERSON> (2013): The brain possibly operates as a \"prediction machine,\" constantly generating models of future experiences based on past patterns.", "neuralReductionLabel": "Neural Reduction", "predictableExperiencesSublabel": "predictable experiences", "unpredictedActivityLabel": "Unpredicted Activity", "neuralActivitySublabel": "neural activity", "organizedMomentTracking": "Organized moment tracking probably creates better predictive models, reducing cognitive load and improving future decision-making accuracy.", "entropyReductionContent": "<PERSON><PERSON><PERSON> et al. (2001): Neural networks using hierarchical processing possibly achieve superior efficiency in information transmission compared to flat structures.", "lifeApplicationEntropy": "Life application: Hierarchical moment organization probably allows extracting maximum insight from experiences while minimizing cognitive noise.", "creativityMythCounterTitle": "Counter-evidence:", "creativityMythCounterContent": "• <PERSON> (2005): Creative professionals with organizational frameworks possibly produce more innovative work\n• <PERSON> (2004): Too many unstructured options probably decrease creative output\n• Hierarchical organization probably reduces cognitive noise, freeing mental resources for creative thinking", "successMythCounterContent": "• <PERSON><PERSON> (2016): Elite performers in all domains probably use highly structured practice and reflection systems\n• High-performing individuals probably show superior organizational skills, not less structure\n• Successful hunter-gatherer societies probably had complex hierarchical organization systems", "hierarchyMythCounterContent": "• All successful primate societies probably exhibit hierarchical organization with clear roles\n• The human brain probably evolved hierarchical processing as its fundamental architecture\n• Even egalitarian societies possibly maintain hierarchical organization for different domains", "simplicityMythCounterContent": "• Appropriate complexity probably matches environmental demands\n• Over-simplification possibly leads to system failure\n• Well-structured complexity probably reduces cognitive load\n• Hierarchical organization probably achieves optimal balance between simplicity and information richness", "anxietyMythCounterContent": "• Unstructured uncertainty probably generates more anxiety than organized complexity\n• Clear frameworks possibly reduce decision anxiety\n• Hierarchical organization probably provides predictive structure that calms the nervous system\n• Studies suggest organizational clarity reduces stress hormones", "fullPresentationMethodologyTitle": "The Methodology", "@fullPresentationMethodologyTitle": {"description": "Title for the methodology page in full presentation"}, "fullPresentationMethodologyContent": "Our approach combines psychological insights with practical analysis:\n\n1. **Moment Identification**: Recognize key moments in your life\n2. **Dimensional Analysis**: Categorize across four levels\n3. **Directional Focus**: Understand inward vs outward orientation\n4. **Impact Assessment**: Evaluate motivation and satisfaction effects\n5. **Pattern Recognition**: Discover your latent values", "@fullPresentationMethodologyContent": {"description": "Content for the methodology page in full presentation"}, "fullPresentationFourLevelsTitle": "Four Levels of Human Experience", "@fullPresentationFourLevelsTitle": {"description": "Title for the four levels page in full presentation"}, "fullPresentationFourLevelsContent": "These levels work hierarchically, building upon each other to create your complete experience.", "@fullPresentationFourLevelsContent": {"description": "Additional content for the four levels page in full presentation"}, "fullPresentationDirectionalFocusTitle": "Directional Focus", "@fullPresentationDirectionalFocusTitle": {"description": "Title for the directional focus page in full presentation"}, "fullPresentationDirectionalFocusContent": "Every moment has both inward and outward components, but one direction typically dominates. Understanding this helps reveal your natural tendencies and preferences.", "@fullPresentationDirectionalFocusContent": {"description": "Additional content for the directional focus page in full presentation"}, "fullPresentationPracticalApplicationTitle": "Practical Application", "@fullPresentationPracticalApplicationTitle": {"description": "Title for the practical application page in full presentation"}, "fullPresentationPracticalApplicationContent": "Use this methodology to:\n\n• **Track patterns** in your decision-making\n• **Identify triggers** for motivation and satisfaction\n• **Understand conflicts** between different aspects of yourself\n• **Make better choices** aligned with your values\n• **Develop strategies** for personal growth", "@fullPresentationPracticalApplicationContent": {"description": "Content for the practical application page in full presentation"}, "fullPresentationConclusionTitle": "Your Journey Begins", "@fullPresentationConclusionTitle": {"description": "Title for the conclusion page in full presentation"}, "fullPresentationConclusionContent": "Now that you understand the methodology, you can:\n\n• Start recording your key moments\n• Analyze your patterns over time\n• Discover your unique latent values\n• Use insights for personal development\n\nRemember: Self-awareness is the first step toward intentional living.", "@fullPresentationConclusionContent": {"description": "Content for the conclusion page in full presentation"}, "fullPresentationElementalDescription": "Physical and instinctual responses", "@fullPresentationElementalDescription": {"description": "Description for elemental level in full presentation"}, "fullPresentationPersonalDescription": "Individual experiences and emotions", "@fullPresentationPersonalDescription": {"description": "Description for personal level in full presentation"}, "fullPresentationInformationalDescription": "Intellectual and analytical processes", "@fullPresentationInformationalDescription": {"description": "Description for informational level in full presentation"}, "fullPresentationSocialDescription": "Interpersonal and cultural connections", "@fullPresentationSocialDescription": {"description": "Description for social level in full presentation"}}