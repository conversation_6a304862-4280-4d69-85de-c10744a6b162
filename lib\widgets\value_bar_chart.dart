import 'package:flutter/material.dart';

class ValueBarChart extends StatelessWidget {
  final Map<String, double> values;
  final Map<String, Color> colors;
  final String title;
  final bool isInward;

  const ValueBarChart({
    super.key,
    required this.values,
    required this.colors,
    required this.title,
    required this.isInward,
  });

  @override
  Widget build(BuildContext context) {
    // Define the pairs organized by hierarchical levels (without showing level titles)
    final levelPairs = [
      // Social level
      ['Altruist', 'Collaborator'],
      // Informational level
      ['Strategist', 'Tactical'],
      // Personal level
      ['Versatile', 'Fun'],
      // Elemental level
      ['Guardian', 'Warrior'],
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Display latent values in side-by-side pairs
        ...levelPairs.map((pair) {
          return Container(
            margin: const EdgeInsets.only(bottom: 24.0),
            child: Row(
              children: pair.map((valueName) {
                final value = values[valueName]?.abs() ?? 0.0;
                final color = colors[valueName] ?? Colors.grey;

                return Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              valueName,
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 14,
                              ),
                            ),
                            const SizedBox(width: 4),
                            GestureDetector(
                              onTap: () => _showValueDescription(context, valueName),
                              child: Icon(
                                Icons.info_outline,
                                size: 16,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${(value * 100).toStringAsFixed(1)}%',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          width: double.infinity,
                          height: 16,
                          decoration: BoxDecoration(
                            color: Colors.grey[300],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: FractionallySizedBox(
                            alignment: Alignment.centerLeft,
                            widthFactor: value,
                            child: Container(
                              decoration: BoxDecoration(
                                color: color,
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          );
        }),
      ],
    );
  }







  void _showValueDescription(BuildContext context, String latentValue) {
    final evolutiveFunction = _getEvolutiveFunction(latentValue);
    final description = _getValueDescription(latentValue);

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            latentValue.toUpperCase(),
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 20,
            ),
          ),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                if (evolutiveFunction.isNotEmpty) ...[
                  Text(
                    'Evolutive Function:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[700],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    evolutiveFunction,
                    style: const TextStyle(
                      fontSize: 14,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                  const SizedBox(height: 16),
                ],
                Text(
                  'Description:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[700],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 14,
                    height: 1.5,
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
          ],
        );
      },
    );
  }

  String _getEvolutiveFunction(String value) {
    switch (value) {
      case 'Guardian':
        return 'Store (Recognize)';
      case 'Warrior':
        return 'Execute (Discard)';
      case 'Versatile':
        return 'Self-Transform (Self-Observe)';
      case 'Fun':
        return 'Self-Enjoy (Self-Motivate)';
      case 'Strategist':
        return 'Predict (Analyze)';
      case 'Tactical':
        return 'Track (Simplify)';
      case 'Altruist':
        return 'Deliberate (Empathize)';
      case 'Collaborator':
        return 'Collaborate (Negotiate)';
      default:
        return '';
    }
  }

  String _getValueDescription(String value) {
    switch (value) {
      case 'Guardian':
        return "Recognize: You recognize, consume, ingest.\nStore: You rest, store, and metabolize.";
      case 'Warrior':
        return "Discard: You discard, flee, inhibit.\nExecute: Fight, attack, and struggle.";
      case 'Versatile':
        return "Self-Observe: You highlight negative information, feel pain, observe errors.\nSelf-Transform: You reduce your errors and adapt stimuli.";
      case 'Fun':
        return "Self-Motivate: You highlight positive information, feel pleasure.\nSelf-Enjoy: I enhance successes and contrast attitudes and ideas.";
      case 'Strategist':
        return "Analyze: You review trends, ask if something could be false, analyze.\nPredict: You predict what is most probable and create hypotheses.";
      case 'Tactical':
        return "Simplify: You generalize, compare the easiest, fastest way.\nTrack: You search, hunt, and trace.";
      case 'Altruist':
        return "Empathize: You empathize with what is important for others.\nDeliberate: You consider myself and the needs of as many people as possible, practicing efficiency altruism.";
      case 'Collaborator':
        return "Negotiate: You help to understand, communicate.\nCollaborate: You cooperate toward shared goals.";
      default:
        return "Description not available.";
    }
  }
}
