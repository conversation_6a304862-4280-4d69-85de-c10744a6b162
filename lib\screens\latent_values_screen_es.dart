import 'package:flutter/material.dart';
import '../widgets/app_menu.dart';
import '../l10n/app_localizations.dart';

class LatentValuesScreenEs extends StatelessWidget {
  const LatentValuesScreenEs({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text('Funciones Evolutivas y Valores'),
        actions: const [
          AppMenu(),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Understanding explanation at the top
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20.0),
              margin: const EdgeInsets.only(bottom: 24.0),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Entendiendo Valores Latentes',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[800],
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'Tus valores latentes emergen de cómo distribuyes tu enfoque entre los cuatro niveles jerárquicos y si tiendes hacia una orientación interna o externa. Cada combinación revela fortalezas centrales y tendencias naturales que guían tu toma de decisiones y satisfacción de vida.',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[700],
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),

            // Header section
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20.0),
              margin: const EdgeInsets.only(bottom: 24.0),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.deepPurple.shade100, Colors.deepPurple.shade50],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.deepPurple.shade200),
              ),
              child: Row(
                children: [
                  Expanded(
                    flex: 1,
                    child: Text(
                      'Funciones Evolutivas',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey[700],
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: Text(
                      'Valores Latentes',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey[700],
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Social Level
            _buildLevelSection(
              context,
              '4. Social',
              const Color(0xFF9F9F9F), // Social level color
              [
                _buildValueRow(
                  context,
                  '8',
                  'Colaborar',
                  'Negociar',
                  'COLABORADOR',
                  'Diplomático',
                  const Color(0xFF254110),
                ),
                _buildValueRow(
                  context,
                  '7',
                  'Deliberar',
                  'Empatizar',
                  'ALTRUISTA',
                  'Empático',
                  const Color(0xFF0F5040),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Informational Level
            _buildLevelSection(
              context,
              '3. Informacional',
              const Color(0xFF808080), // Informational level color
              [
                _buildValueRow(
                  context,
                  '6',
                  'Predecir',
                  'Analizar',
                  'ESTRATEGA',
                  'Analista',
                  const Color(0xFF1E293B),
                ),
                _buildValueRow(
                  context,
                  '5',
                  'Rastrear',
                  'Simplificar',
                  'TÁCTICO',
                  'Sintetizador',
                  const Color(0xFF5E4E0F),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Personal Level
            _buildLevelSection(
              context,
              '2. Personal',
              const Color(0xFF595959), // Personal level color
              [
                _buildValueRow(
                  context,
                  '4',
                  'Auto-Disfrutar',
                  'Auto-Motivar',
                  'DIVERTIDO',
                  'Entusiasta',
                  const Color(0xFF794B19),
                ),
                _buildValueRow(
                  context,
                  '3',
                  'Auto-Transformar',
                  'Auto-Observar',
                  'VERSÁTIL',
                  'Auto-Observador',
                  const Color(0xFF332444),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Elemental Level
            _buildLevelSection(
              context,
              '1. Elemental',
              const Color(0xFF404040), // Elemental level color
              [
                _buildValueRow(
                  context,
                  '2',
                  'Ejecutar',
                  'Descartar',
                  'GUERRERO',
                  'Liberador',
                  const Color(0xFF5B1106),
                ),
                _buildValueRow(
                  context,
                  '1',
                  'Almacenar',
                  'Reconocer',
                  'GUARDIÁN',
                  'Cuidador',
                  const Color(0xFF6A0F36),
                ),
              ],
            ),

          ],
        ),
      ),
    );
  }

  Widget _buildLevelSection(
    BuildContext context,
    String levelTitle,
    Color levelColor,
    List<Widget> valueRows,
  ) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        children: [
          // Level header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              color: levelColor,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Text(
              levelTitle,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          // Value rows
          ...valueRows,
        ],
      ),
    );
  }

  Widget _buildValueRow(
    BuildContext context,
    String number,
    String function,
    String subFunction,
    String latentValue,
    String latentSubtitle,
    Color latentColor,
  ) {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200, width: 1),
        ),
      ),
      child: Row(
        children: [
          // Left side - Evolutive Functions
          Expanded(
            flex: 1,
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                border: Border(
                  right: BorderSide(color: Colors.grey.shade300, width: 1),
                ),
              ),
              child: GestureDetector(
                onTap: () => _showValueDescription(context, latentValue),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: latentColor,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Center(
                        child: Text(
                          number,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            subFunction,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            function,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: latentColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Icon(
                      Icons.info_outline,
                      color: Colors.grey[600],
                      size: 20,
                    ),
                  ],
                ),
              ),
            ),
          ),
          // Right side - Latent Value
          Expanded(
            flex: 1,
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: latentColor,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    latentSubtitle,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.white.withValues(alpha: 0.8),
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    latentValue,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 1.2,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showValueDescription(BuildContext context, String latentValue) {
    String description;

    switch (latentValue) {
      case 'GUARDIÁN':
        description = 'Valor enfocado en preservar, proteger y mantener estabilidad. Representa la capacidad de reconocer y almacenar información importante para seguridad y continuidad.';
        break;
      case 'GUERRERO':
        description = 'Valor enfocado en acción, ejecución y superación de obstáculos. Representa la capacidad de ejecutar decisiones y descartar lo que ya no sirve.';
        break;
      case 'VERSÁTIL':
        description = 'Valor enfocado en adaptabilidad y auto-observación. Representa la capacidad de transformarse y observarse a sí mismo para crecimiento personal.';
        break;
      case 'DIVERTIDO':
        description = 'Valor enfocado en placer, motivación y energía positiva. Representa la capacidad de auto-disfrutar y auto-motivarse para mantener el bienestar.';
        break;
      case 'ESTRATEGA':
        description = 'Valor enfocado en planificación y análisis profundo. Representa la capacidad de predecir escenarios y analizar información compleja.';
        break;
      case 'TÁCTICO':
        description = 'Valor enfocado en síntesis y rastreo eficiente. Representa la capacidad de simplificar complejidades y rastrear progreso.';
        break;
      case 'ALTRUISTA':
        description = 'Valor enfocado en empatía y consideración por otros. Representa la capacidad de deliberar y empatizar para el bien común.';
        break;
      case 'COLABORADOR':
        description = 'Valor enfocado en cooperación y negociación. Representa la capacidad de colaborar efectivamente y negociar soluciones mutuamente beneficiosas.';
        break;
      default:
        description = 'Descripción no disponible.';
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            latentValue,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 20,
            ),
          ),
          content: SingleChildScrollView(
            child: Text(
              description,
              style: const TextStyle(
                fontSize: 16,
                height: 1.5,
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('Cerrar'),
            ),
          ],
        );
      },
    );
  }
}