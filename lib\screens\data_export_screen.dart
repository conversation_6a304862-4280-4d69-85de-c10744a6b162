import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/moments_provider.dart';
import '../services/csv_export_service.dart';
import '../widgets/app_menu.dart';

class DataExportScreen extends StatefulWidget {
  const DataExportScreen({super.key});

  @override
  State<DataExportScreen> createState() => _DataExportScreenState();
}

class _DataExportScreenState extends State<DataExportScreen> {
  bool _isExporting = false;
  String? _exportedFilePath;
  String? _errorMessage;
  String? _csvData; // Store CSV data if file export fails

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Export Data'),
        actions: const [
          AppMenu(),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Export Options',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: ListTile(
                leading: const Icon(Icons.file_download),
                title: const Text('Export to CSV'),
                subtitle: const Text('Save your moments data as a CSV file'),
                onTap: _isExporting ? null : _exportToCSV,
              ),
            ),
            const SizedBox(height: 24),
            if (_isExporting)
              const Center(
                child: CircularProgressIndicator(),
              )
            else if (_exportedFilePath != null && _csvData == null)
              Card(
                color: Colors.green[100],
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Export Successful!',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text('File saved to: $_exportedFilePath'),
                      const SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          ElevatedButton.icon(
                            onPressed: () => _viewExportedFile(_exportedFilePath!),
                            icon: const Icon(Icons.visibility),
                            label: const Text('View File'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              )
            else if (_csvData != null)
              Card(
                color: Colors.green[100],
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Export Successful!',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Text('CSV data exported successfully:'),
                      const SizedBox(height: 8),
                      Container(
                        height: 200,
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: SingleChildScrollView(
                          child: Text(_csvData!),
                        ),
                      ),
                    ],
                  ),
                ),
              )
            else if (_errorMessage != null)
              Card(
                color: Colors.red[100],
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Export Failed',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.red,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(_errorMessage!),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Future<void> _exportToCSV() async {
    setState(() {
      _isExporting = true;
      _exportedFilePath = null;
      _errorMessage = null;
    });

    try {
      final momentsProvider = Provider.of<MomentsProvider>(context, listen: false);
      final moments = momentsProvider.moments;

      if (moments.isEmpty) {
        setState(() {
          _isExporting = false;
          _errorMessage = 'No moments to export.';
        });
        return;
      }

      final csvExportService = CSVExportService();
      final result = await csvExportService.exportToCSV(moments);

      // Check if the result is a file path or CSV data
      if (result.startsWith('/') || result.contains('\\')) {
        // It's a file path
        setState(() {
          _isExporting = false;
          _exportedFilePath = result;
        });
      } else {
        // It's CSV data
        setState(() {
          _isExporting = false;
          _exportedFilePath = 'CSV data exported successfully';
          _csvData = result;
        });
      }
    } catch (e) {
      setState(() {
        _isExporting = false;
        _errorMessage = 'Error exporting data: $e';
      });
    }
  }

  Future<void> _viewExportedFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        if (!mounted) return;
        Navigator.pushNamed(
          context,
          '/csv_viewer',
          arguments: filePath,
        );
      } else {
        if (!mounted) return;
        setState(() {
          _errorMessage = 'File not found: $filePath';
          _exportedFilePath = null;
        });
      }
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _errorMessage = 'Error opening file: $e';
        _exportedFilePath = null;
      });
    }
  }


}
